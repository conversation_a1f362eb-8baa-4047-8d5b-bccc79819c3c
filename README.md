# seqmag-ui

## Cloning the Repository

1. Clone this repository:

```
git clone https://github.com/TrainGenomics/seqmag-ui.git
```

## Preparing the Environment

1. Install Chrome if needed

```
wget -O /tmp/chrome.deb https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_139.0.7258.66-1_amd64.deb
sudo apt install /tmp/chrome.deb
rm /tmp/chrome.deb
```

## Build Instructions

1. From the project root, execute:  
```
./build.sh
```

The build output will be generated in `build/`.

## Usage

1. Change to the build directory and execute:  
```
cd build/
./seqmag-report-gen.sh /path/to/report.json report.pdf
```

to generate `report.pdf` in the current directory.

2. Observe output similar to:
```
Running: google-chrome --headless --disable-gpu --print-to-pdf=report.pdf --no-pdf-header-footer --run-all-compositor-stages-before-draw --virtual-time-budget=10000 file:///mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
[153:179:0816/122131.809460:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:182:0816/122131.835074:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:182:0816/122131.836657:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:179:0816/122131.904046:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122131.904115:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122131.904133:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122131.904138:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122131.969176:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122132.009962:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:153:0816/122132.220689:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type:
[153:153:0816/122132.222711:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type:
[153:179:0816/122132.222839:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:179:0816/122132.222868:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[153:153:0816/122132.223116:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type:
[153:245:0816/122132.251326:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:245:0816/122132.251456:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:245:0816/122132.251573:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:245:0816/122132.251624:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:245:0816/122132.251640:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /run/dbus/system_bus_socket: No such file or directory
[153:153:0816/122132.260089:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type:
[153:153:0816/122132.261720:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type:
1094850 bytes written to file report.pdf
Temporary HTML file cleaned up: /mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
Wrote report.pdf (analysis_id=f81d4fae-7dec-11d0-a765-00a0c91e6bf6)
```

## Running Acceptance Tests

1. Install `diff-pdf` if needed

```
sudo apt install diff-pdf-wx
```

2. From the project root, execute:  
```
./tools/run-acceptance-tests.sh
```

3. Observe output similar to:  
```
SeqMag Report Generator - Acceptance Tests
==============================================

Checking prerequisites...
✓ Prerequisites check passed

Running acceptance tests...

Found 5 test file(s)

Test 1: invalid-report-empty
  Negative test detected (expects non-zero exit from generator).
  ✓ PASS: seqmag-report-gen failed as expected (exit 2)

Test 2: invalid-report-missing-specimen
  Negative test detected (expects non-zero exit from generator).
  ✓ PASS: seqmag-report-gen failed as expected (exit 2)

Test 3: invalid-report-wrong-spec-target
  Negative test detected (expects non-zero exit from generator).
  ✓ PASS: seqmag-report-gen failed as expected (exit 2)

Test 4: valid-report-1
  Generating PDF from /mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-1.json...
Running: google-chrome --headless --disable-gpu --print-to-pdf=/mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-1.generated.pdf --no-pdf-header-footer --run-all-compositor-stages-before-draw --virtual-time-budget=10000 file:///mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
Temporary HTML file cleaned up: /mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
Wrote /mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-1.generated.pdf (analysis_id=f81d4fae-7dec-11d0-a765-00a0c91e6bf6)
  Comparing with expected PDF...
  ✓ PASS: PDFs match

Test 5: valid-report-2
  Generating PDF from /mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-2.json...
Running: google-chrome --headless --disable-gpu --print-to-pdf=/mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-2.generated.pdf --no-pdf-header-footer --run-all-compositor-stages-before-draw --virtual-time-budget=10000 file:///mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
Temporary HTML file cleaned up: /mnt/c/data/wDev/train/seqmag-report-gen/main/build/template/filled_f81d4fae-7dec-11d0-a765-00a0c91e6bf6.html
Wrote /mnt/c/data/wDev/train/seqmag-report-gen/main/aat/valid-report-2.generated.pdf (analysis_id=f81d4fae-7dec-11d0-a765-00a0c91e6bf6)
  Comparing with expected PDF...
  ✓ PASS: PDFs match

==============================================
Test Summary
==============================================
Total tests:  5
Passed:       5
Failed:       0
```