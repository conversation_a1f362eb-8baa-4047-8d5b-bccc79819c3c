# seqmag-ui

## Build Instructions

1. Clone this repository:

```
git clone https://github.com/TrainGenomics/seqmag-ui.git
```

2. Install development dependencies:
```
./tools/install-dev-dependencies.sh
```
3. Build:
```
./build.sh
```

The build output will be generated in `build/`.


## Running

1. Serve the build output directory with a web server. For example, from the project root:
```
./tools/serve-www.sh &
```
2. Navigate to `http://localhost:8000` in a web browser.


## Usage

Upon loading the page, the browser will automatically open the browser's print dialog. The following print dialog
settings must be set for accurate report formatting:

* **Disable** "Print Headers and Footers"
* **Enable** "Print Backgrounds"

Select a PDF print destination of your choice (e.g. Save as PDF) and click "Print."
