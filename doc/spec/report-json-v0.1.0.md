# SeqMag Report JSON Format Specification v0.1.0

## Overview

This document specifies the JSON format for SeqMag analysis reports. The format is designed to capture comprehensive 
information about pathogen detection results, patient information, test performance metrics, and clinical 
interpretations.

## Report File Characteristics

- **Format**: JSON (RFC 7159)
- **Encoding**: UTF-8

## Root Object Structure

The root JSON object contains the following required fields:

```
{
  "specification_target_version": { ... },
  "analysis": { ... },
  "specimen": { ... },
  "patient": { ... },
  "ordered": { ... },
  "run_summary": { ... },
  "detections": [ ... ],
  "interpretations": [ ... ],
  "test_performance": { ... }
}
```

## Field Specifications

### specification_target_version (Required)

Specifies the version of the specification that the report conforms to. To conform to the specification defined by this 
file the value must be:
```json
{
  "major": 0,
  "minor": 1
}
```

**Type**: Object
**Required Fields**:
- `major` (integer): Major version number
- `minor` (integer): Minor version number

**Example**:
```json
{
  "major": 0,
  "minor": 1
}
```

### analysis (Required)

Contains analysis-specific metadata.

**Type**: Object
**Required Fields**:
- `id` (string): Unique identifier for the analysis (UUID format)

**Example**:
```json
{
  "id": "f81d4fae-7dec-11d0-a765-00a0c91e6bf6"
}
```

### specimen (Required)

Information about the biological specimen analyzed.

**Type**: Object
**Required Fields**:
- `type` (string): Type of specimen. Valid values: `"plasma"`, `"blood"` only
- `id` (string): Unique identifier for the specimen
- `collection_date` (object): Date when specimen was collected

**collection_date Object**:
- `year` (integer): Four-digit year
- `month` (integer): Month (1-12)
- `day_of_month` (integer): Day of month (1-31)

**Example**:
```json
{
  "type": "plasma",
  "id": "12345678",
  "collection_date": {
    "year": 2025,
    "month": 7,
    "day_of_month": 15
  }
}
```

### patient (Required)

Patient demographic and identification information.

**Type**: Object
**Required Fields**:
- `name` (object): Patient name information
- `date_of_birth` (object): Patient date of birth
- `mrn` (string): Medical record number

**name Object**:
- `first` (string): First name
- `last` (string): Last name

**date_of_birth Object**:
- `year` (integer): Four-digit birth year
- `month` (integer): Birth month (1-12)
- `day_of_month` (integer): Birth day of month (1-31)

**Example**:
```json
{
  "name": {
    "first": "John",
    "last": "Smith"
  },
  "date_of_birth": {
    "year": 1990,
    "month": 1,
    "day_of_month": 1
  },
  "mrn": "12345678"
}
```

### ordered (Required)

Information about who ordered the test and where.

**Type**: Object
**Required Fields**:
- `by_person` (object): Information about the ordering clinician
- `at_facility` (object): Information about the ordering facility

**by_person Object**:
- `name` (object): Clinician name information
  - `first` (string): First name
  - `last` (string): Last name
  - `title` (string): Professional title (e.g., "Dr.")
  - `post_nominal` (string): Post-nominal credentials (e.g., "M.D.")

**at_facility Object**:
- `display_name` (string): Facility name
- `address` (object): Facility address
  - `street` (string): Street address
  - `locality` (string): City/locality
  - `region` (string): State/region
  - `postal` (string): Postal/ZIP code

**Example**:
```json
{
  "by_person": {
    "name": {
      "first": "Gregory",
      "last": "House",
      "title": "Dr.",
      "post_nominal": "M.D."
    }
  },
  "at_facility": {
    "display_name": "Anytown Medical Center",
    "address": {
      "street": "12345 Main St",
      "locality": "Anytown",
      "region": "Anystate",
      "postal": "77030"
    }
  }
}
```

### run_summary (Required)

Summary of the sequencing run and analysis.

**Type**: Object
**Required Fields**:
- `status` (string): Run status (e.g., "success")
- `total_reads` (integer): Total number of sequencing reads
- `software_version` (object): Version of SegMag analysis software
- `mapped_reads` (integer): Number of reads that mapped to references
- `mapped_reads_percentage` (number): Percentage of reads that mapped (0.0-1.0)

**software_version Object**:
- `major` (integer): Major version number
- `minor` (integer): Minor version number
- `patch` (integer): Patch version number

**Example**:
```json
{
  "status": "success",
  "total_reads": 120000000,
  "software_version": {
    "major": 1,
    "minor": 0,
    "patch": 0
  },
  "mapped_reads": 900000,
  "mapped_reads_percentage": 0.9
}
```

### detections (Required)

Array of pathogen detection results.

**Type**: Array of objects
**Required Fields** (for each detection object):
- `reference_genome` (string): Species name of the detected pathogen
- `type` (string): Type of pathogen. Valid values: `"bacteria"`, `"virus"`, `"fungus"`, `"parasite"` only
- `confidence` (number): Detection confidence score (0.0-1.0)
- `clinical_significance` (string): Clinical significance. Valid values: `"Disease associated"`, `
  "Commensal/lab strain"` only
- `coverage_score` (number): Genome coverage score (0.0-1.0)
- `SoC_count` (integer): Strength of Confidence count
- `alert_level` (string): Alert priority level. Valid values: `"low"`, `"medium"`, `"high"` only

**Example**:
```json
[
  {
    "reference_genome": "Kaposi sarcoma-associated herpesvirus",
    "type": "parasite",
    "confidence": 0.98,
    "clinical_significance": "Commensal/lab strain",
    "coverage_score": 0.98,
    "SoC_count": 1622,
    "alert_level": "low"
  },
  {
    "reference_genome": "Bacteroides fragillis",
    "type": "bacteria",
    "confidence": 0.91,
    "clinical_significance": "Disease associated",
    "coverage_score": 0.67,
    "SoC_count": 181,
    "alert_level": "high"
  }
]
```

### interpretations (Required)

Array of clinical interpretation notes.

**Type**: Array of objects
**Required Fields** (for each interpretation object):
- `text` (string): Interpretation text content
- `style` (string): Text styling. Valid values: `"normal"`, `"bold"` only

**Example**:
```json
[
  {
    "text": "High-confidence detection of *K. variicola* suggests potential gram-negative infection.",
    "style": "normal"
  },
  {
    "text": "Significant fungal infection detection, high risk to humans.",
    "style": "bold"
  }
]
```

### test_performance (Required)

Information about test performance characteristics and methodology.

**Type**: Object
**Required Fields**:
- `assay` (string): Description of the assay performed
- `turnaround_time` (object): Time breakdown for different phases
- `limit_of_detection` (string): Detection limit description
- `sequencing_platform` (string): Sequencing platform used
- `reads_processed` (string): Description of reads processed

**turnaround_time Object**:
- `prep_hours` (integer): Hours for sample preparation
- `sequencing_hours` (integer): Hours for sequencing
- `analysis_hours` (integer): Hours for analysis

**Example**:
```json
{
  "assay": "Shotgun metagenomic sequencing (mNGS)",
  "turnaround_time": {
    "prep_hours": 1,
    "sequencing_hours": 10,
    "analysis_hours": 1
  },
  "limit_of_detection": "~10 CFU/mL for most organisms",
  "sequencing_platform": "Illumina NextSeq 2000",
  "reads_processed": "120 million (2x250bp)"
}
```

## Validation Rules

### General Rules
1. All required fields must be present
2. Field types must match the specified types
3. Enum values must match exactly (case-sensitive)
4.Dates must be valid calendar dates

### Specific Validation Rules
- **UUIDs**: Analysis ID should follow UUID format
- **Dates**: Year must be 4 digits, month 1-12, day_of_month 1-31
- **Confidence/Coverage Scores**: Must be between 0.0 and 1.0
- **Counts**: Must be non-negative integers
- **Percentages**: Must be between 0.0 and 1.0