"""
HTML manipulation utilities for SeqMag report generation.

This module provides utility functions for manipulating HTML content,
including pattern matching and content replacement operations.
"""

import re

def replace_html_element_content_by_id(html: str, element_id: str, new_content: str) -> str:
    """
    Replace the content of an HTML element with a specific ID.

    Args:
        html: The HTML string to modify
        element_id: The ID of the element to find and replace
        new_content: The new content to insert

    Returns:
        str: The HTML with the element content replaced
    """
    return re.sub(
        rf'(<[^>]*id\s*=\s*["\']' + re.escape(element_id) + r'["\'][^>]*>)(.*?)(</[^>]*>)',
        lambda m:f"{m.group(1)}{new_content}{m.group(3)}",
        html,
        flags=re.IGNORECASE | re.DOTALL
    )

def replace_html_element_content_by_class(html: str, element_class: str, new_content: str) -> str:
    """
    Replace the content of HTML elements with a specific CSS class.

    Args:
        html: The HTML string to modify
        element_class: The CSS class name of elements to find and replace
        new_content: The new content to insert

    Returns:
        str: The HTML with the element contents replaced
    """
    # Pattern matches elements that have the specified class (handles multiple classes)
    pattern = rf'(<[^>]*class\s*=\s*["\'][^"\']*\b' + re.escape(element_class) + r'\b[^"\']*["\'][^>]*>)(.*?)(</[^>]*>)'
    return re.sub(
        pattern,
        lambda m: f"{m.group(1)}{new_content}{m.group(3)}",
        html,
        flags=re.IGNORECASE | re.DOTALL
    )
