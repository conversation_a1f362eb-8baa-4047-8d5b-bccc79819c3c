from __future__ import annotations

import argparse
import sys
from pathlib import Path

from .report_data import ReportData
from .report_data_parser import parse_report_json
from .html_template_populator import populate_html_template
from .chrome import run_chrome_pdf_generation


def get_default_template_directory() -> Path:
    """
    Get the default template directory path (of a post build layout).

    Returns the path to the template directory relative to this package.
    Does not create the directory if it doesn't exist.

    Returns:
        Path: The path to the default template directory.
    """
    return Path(__file__).parent.parent / "template"


def get_default_html_template_text() -> str:
    """
    Load the default HTML template from the package resources.

    Reads the built-in HTML template file that is packaged with the application.
    This template is used when no custom template is specified by the user.

    Returns:
        str: The content of the default HTML template as a string.

    Raises:
        FileNotFoundError: If the default template file cannot be found in the package.
        IOError: If there's an error reading the template file.
    """
    template_path: Path = get_default_template_directory() / "report.html"
    return template_path.read_text(encoding="utf-8")





def parse_args(argv: list[str]) -> argparse.Namespace:
    """
    Parse command-line arguments for the seqmag-report-gen application.

    Sets up and executes argument parsing for the report generation tool.
    Defines two arguments: required JSON input file and required PDF output file.
    The HTML template is always the default built-in template.

    Args:
        argv (list[str]): List of command-line arguments to parse, typically
                         from sys.argv[1:].

    Returns:
        argparse.Namespace: Parsed arguments object with attributes:
            - report_json_path (Path): Path to the input JSON file
            - output_pdf_path (Path): Path for the output PDF file

    Raises:
        SystemExit: If argument parsing fails (invalid arguments, --help, etc.)
    """
    p: argparse.ArgumentParser = argparse.ArgumentParser(
        prog="seqmag-report-gen",
        description="Fill an HTML template from JSON and export to PDF via headless Chrome/Chromium.",
    )
    p.add_argument("report_json_path", type=Path, help="Path to JSON file with values for the template.")
    p.add_argument("output_pdf_path", type=Path, help="Path to the output PDF file.")
    return p.parse_args(argv)


def main(argv: list[str]) -> int:
    """
    Main entry point for the seqmag-report-gen command-line application.

    Orchestrates the complete report generation workflow:
    1. Parse command-line arguments
    2. Load and validate JSON report data
    3. Load default HTML template
    4. Render the template with report data
    5. Generate PDF using headless Chrome/Chromium

    Args:
        argv (list[str]): Command-line arguments, typically from sys.argv[1:].
                         Expected contents:
                         - argv[0]: Path to JSON file with report data (required)
                         - argv[1]: Path for output PDF file (required)

                         Examples:
                         - ["report.json", "output.pdf"]
                         - ["data/report.json", "reports/output.pdf"]

    Returns:
        int: Exit code for the application:
            - 0: Success - PDF generated successfully
            - 1: PDF generation failed (Chrome/Chromium error)
            - 2: Input validation failed (JSON or template error)

    Side Effects:
        - Creates the output PDF file at the specified path
        - Creates parent directories for output file if they don't exist
        - Creates and cleans up temporary HTML file after PDF generation
        - Prints success message with analysis ID to stdout
        - Prints cleanup confirmation and error messages to stderr on failure

    Example:
        >>> main(["report.json", "output.pdf"])
        Wrote output.pdf (analysis_id=550e8400-e29b-41d4-a716-446655440000)
        0
    """
    args: argparse.Namespace = parse_args(argv)

    # Read and parse JSON into ReportData
    try:
        with args.report_json_path.open("r", encoding="utf-8") as f:
            report_data: ReportData = parse_report_json(f)
    except Exception as e:
        print(f"Error reading JSON from {args.report_json_path}: {e}", file=sys.stderr)
        return 2

    # Load default template and template directory
    try:
        report_html_template = get_default_html_template_text()
        template_dir = get_default_template_directory()
    except Exception as e:
        print(f"Error reading template HTML: {e}", file=sys.stderr)
        return 2

    # Load header, footer, page content, and red alert container HTML files
    try:
        header_html_path = template_dir / "header.html"
        footer_html_path = template_dir / "footer.html"
        page1_html_path = template_dir / "page1.html"
        page2_html_path = template_dir / "page2.html"
        page3_html_path = template_dir / "page3.html"
        red_alert_container_html_path = template_dir / "red-alert-container.html"
        yellow_alert_container_html_path = template_dir / "yellow-alert-container.html"
        green_alert_container_html_path = template_dir / "green-alert-container.html"

        header_html: str = header_html_path.read_text(encoding="utf-8")
        footer_html: str = footer_html_path.read_text(encoding="utf-8")
        page1_html: str = page1_html_path.read_text(encoding="utf-8")
        page2_html: str = page2_html_path.read_text(encoding="utf-8")
        page3_html: str = page3_html_path.read_text(encoding="utf-8")
        red_alert_container_html: str = red_alert_container_html_path.read_text(encoding="utf-8")
        yellow_alert_container_html: str = yellow_alert_container_html_path.read_text(encoding="utf-8")
        green_alert_container_html: str = green_alert_container_html_path.read_text(encoding="utf-8")
    except Exception as e:
        print(f"Error reading HTML template files: {e}", file=sys.stderr)
        return 2

    # Render HTML
    filled_html: str = populate_html_template(report_html_template, header_html, footer_html, page1_html, page2_html, page3_html, red_alert_container_html, yellow_alert_container_html, green_alert_container_html, report_data)

    # Ensure output directory exists
    args.output_pdf_path.parent.mkdir(parents=True, exist_ok=True)

    # Write temp HTML file inside the template folder, then print to PDF
    try:
        # Create temporary HTML file inside the template directory
        tmp_html_path: Path = template_dir / f"filled_{report_data.analysis_id}.html"
        tmp_html_path.write_text(filled_html, encoding="utf-8")

        # Generate PDF from temporary HTML file
        run_chrome_pdf_generation(args.output_pdf_path, tmp_html_path)

        # Clean up temporary HTML file
#        if tmp_html_path.exists():
#            tmp_html_path.unlink()
#            print(f"Temporary HTML file cleaned up: {tmp_html_path}")
#        else:
#            print(f"Temporary HTML file not found for cleanup: {tmp_html_path}")
    except Exception as e:
        print(f"Failed to generate PDF: {e}", file=sys.stderr)
        return 1

    print(f"Wrote {args.output_pdf_path} (analysis_id={report_data.analysis_id})")
    return 0
