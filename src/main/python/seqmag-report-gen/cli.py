from __future__ import annotations

import argparse
import sys
from pathlib import Path

from .report_data import ReportData
from .report_data_parser import parse_report_json
from .html_template_populator import populate_html_template
from .chrome import run_chrome_pdf_generation


def get_default_template_directory() -> Path:
    """
    Get the default template directory path (of a post build layout).

    Returns the path to the template directory relative to this package.
    Does not create the directory if it doesn't exist.

    Returns:
        Path: The path to the default template directory.
    """
    return Path(__file__).parent.parent / "web"


def get_default_html_template_text() -> str:
    """
    Load the default HTML template from the package resources.

    Reads the built-in HTML template file that is packaged with the application.
    This template is used when no custom template is specified by the user.

    Returns:
        str: The content of the default HTML template as a string.

    Raises:
        FileNotFoundError: If the default template file cannot be found in the package.
        IOError: If there's an error reading the template file.
    """
    template_path: Path = get_default_template_directory() / "report.html"
    return template_path.read_text(encoding="utf-8")


def get_default_template_path_hint() -> str:
    """
    Get a human-readable description of the default template location.

    This function provides a user-friendly string that describes where the
    default template is located, used in help messages and documentation.

    Returns:
        str: A descriptive string indicating the default template location.
    """
    template_path: Path = get_default_template_directory() / "index.html"
    return f"built-in package template ({template_path})"


def parse_args(argv: list[str]) -> argparse.Namespace:
    """
    Parse command-line arguments for the seqmag-report-gen application.

    Sets up and executes argument parsing for the report generation tool.
    Defines three arguments: required JSON input file, required PDF output file,
    and optional HTML template file.

    Args:
        argv (list[str]): List of command-line arguments to parse, typically
                         from sys.argv[1:].

    Returns:
        argparse.Namespace: Parsed arguments object with attributes:
            - report_json_path (Path): Path to the input JSON file
            - output_pdf_path (Path): Path for the output PDF file
            - input_html_path (Path | None): Path to HTML template, or None for default

    Raises:
        SystemExit: If argument parsing fails (invalid arguments, --help, etc.)
    """
    p: argparse.ArgumentParser = argparse.ArgumentParser(
        prog="seqmag-report-gen",
        description="Fill an HTML template from JSON and export to PDF via headless Chrome/Chromium.",
    )
    p.add_argument("report_json_path", type=Path, help="Path to JSON file with values for the template.")
    p.add_argument("output_pdf_path", type=Path, help="Path to the output PDF file.")
    p.add_argument(
        "input_html_path",
        nargs="?",
        type=Path,
        help=f"Optional path to HTML template. Default: {get_default_template_path_hint()}",
    )
    return p.parse_args(argv)


def main(argv: list[str]) -> int:
    """
    Main entry point for the seqmag-report-gen command-line application.

    Orchestrates the complete report generation workflow:
    1. Parse command-line arguments
    2. Load and validate JSON report data
    3. Load HTML template (custom or default)
    4. Render the template with report data
    5. Generate PDF using headless Chrome/Chromium

    Args:
        argv (list[str]): Command-line arguments, typically from sys.argv[1:].
                         Expected contents:
                         - argv[0]: Path to JSON file with report data (required)
                         - argv[1]: Path for output PDF file (required)
                         - argv[2]: Path to HTML template file (optional)

                         Examples:
                         - ["report.json", "output.pdf"]
                         - ["data/report.json", "reports/output.pdf", "template/custom.html"]

    Returns:
        int: Exit code for the application:
            - 0: Success - PDF generated successfully
            - 1: PDF generation failed (Chrome/Chromium error)
            - 2: Input validation failed (JSON or template error)

    Side Effects:
        - Creates the output PDF file at the specified path
        - Creates parent directories for output file if they don't exist
        - Creates and cleans up temporary HTML file after PDF generation
        - Prints success message with analysis ID to stdout
        - Prints cleanup confirmation and error messages to stderr on failure

    Example:
        >>> main(["report.json", "output.pdf"])
        Wrote output.pdf (analysis_id=550e8400-e29b-41d4-a716-446655440000)
        0
    """
    args: argparse.Namespace = parse_args(argv)

    # Read and parse JSON into ReportData
    try:
        with args.report_json_path.open("r", encoding="utf-8") as f:
            report_data: ReportData = parse_report_json(f)
    except Exception as e:
        print(f"Error reading JSON from {args.report_json_path}: {e}", file=sys.stderr)
        return 2

    # Load template and determine template directory
    try:
        if args.input_html_path:
            report_html_template: str = args.input_html_path.read_text(encoding="utf-8")
            template_dir: Path = args.input_html_path.parent
        else:
            report_html_template = get_default_html_template_text()
            # For default template, use the default template directory
            template_dir = get_default_template_directory()
    except Exception as e:
        print(f"Error reading template HTML: {e}", file=sys.stderr)
        return 2

    # Load header and footer HTML files
    try:
        header_html_path = template_dir / "header.html"
        footer_html_path = template_dir / "footer.html"

        header_html: str = header_html_path.read_text(encoding="utf-8")
        footer_html: str = footer_html_path.read_text(encoding="utf-8")
    except Exception as e:
        print(f"Error reading header/footer HTML files: {e}", file=sys.stderr)
        return 2

    # Render HTML
    filled_html: str = populate_html_template(report_html_template, header_html, footer_html, report_data)

    # Ensure output directory exists
    args.output_pdf_path.parent.mkdir(parents=True, exist_ok=True)

    # Write temp HTML file inside the template folder, then print to PDF
    try:
        # Create temporary HTML file inside the template directory
        tmp_html_path: Path = template_dir / f"filled_{report_data.analysis_id}.html"
        tmp_html_path.write_text(filled_html, encoding="utf-8")

        # Generate PDF from temporary HTML file
        run_chrome_pdf_generation(args.output_pdf_path, tmp_html_path)

        # Clean up temporary HTML file
        if tmp_html_path.exists():
            tmp_html_path.unlink()
            print(f"Temporary HTML file cleaned up: {tmp_html_path}")
        else:
            print(f"Temporary HTML file not found for cleanup: {tmp_html_path}")
    except Exception as e:
        print(f"Failed to generate PDF: {e}", file=sys.stderr)
        return 1

    print(f"Wrote {args.output_pdf_path} (analysis_id={report_data.analysis_id})")
    return 0
