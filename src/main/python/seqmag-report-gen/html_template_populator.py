from __future__ import annotations

import re
from .report_data import ReportData, CalendarDate, OrderingPersonName, PhysicalAddress, SoftwareVersion, Detection, AlertLevel, InterpretationNote, TextStyle, TestPerformance, TurnaroundTime
from .page1_template import Page1Template
from .page2_template import Page2Template
from .page3_template import Page3Template


def populate_html_template(report_html_template: str, header_html: str, footer_html: str, report: ReportData) -> str:
    """
    Find HTML elements with specific IDs and replace their inner text with corresponding report values.

    Args:
        report_html_template: The main HTML template content (report.html)
        header_html: The header HTML template content (header.html)
        footer_html: The footer HTML template content (footer.html)
        report: The report data to populate into the templates

    Returns:
        str: The populated HTML with all template content and data filled in

    Currently populates:
    - id="specimen-type": Filled with specimen type value (e.g., "plasma", "blood")
    - id="specimen-id": Filled with specimen ID value
    - id="collection-date": Filled with specimen collection date in YYYY-MM-DD format
    - id="patient-first-name": Filled with patient's first name
    - id="patient-last-name": Filled with patient's last name
    - id="patient-date-of-birth": Filled with patient's date of birth in YYYY-MM-DD format
    - id="patient-mrn": Filled with patient's medical record number
    - id="ordering-clinician": Filled with ordering clinician's full name and credentials
    - id="facility-name": Filled with facility display name
    - id="facility-address-1": Filled with facility street address
    - id="facility-address-2": Filled with facility city, state, and postal code
    - id="run-status": Filled with run status (e.g., "success")
    - id="reads-processed-meta": Filled with total reads processed (human-readable format like "120 million")
    - id="software-version": Filled with software version in vX.Y.Z format
    - id="reads-table": Table populated with detection results (tbody filled with detection rows)
    - id="red-alert-non-icons-container": Div populated with alert rows for detections with alert_level HIGH
    - id="yellow-alert-non-icons-container": Div populated with alert rows for detections with alert_level MEDIUM
    - id="green-alert-non-icons-container": Div populated with alert rows for detections with alert_level LOW
    - id="interpretation-list": UL populated with interpretation notes as list items
    - id="assay": Filled with test performance assay type
    - id="turnaround-time": Filled with formatted turnaround time breakdown
    - id="limit-of-detection": Filled with limit of detection information
    - id="sequencing-platform": Filled with sequencing platform information
    - id="reads-processed-performance": Filled with reads processed information
    - id="software-version-database": Filled with software version in vX.Y.Z format

    Args:
        html_template (str): The HTML template content containing elements with specific IDs.
        report (ReportData): The report data containing specimen, analysis, and patient information.

    Returns:
        str: The HTML with element contents replaced by actual report values.

    Example:
        >>> html = '''<div id="specimen-type">placeholder</div>
        ... <span id="patient-first-name">placeholder</span>
        ... <table id="reads-table"><tbody></tbody></table>
        ... <div id="red-alert-non-icons-container"></div>
        ... <div id="yellow-alert-non-icons-container"></div>
        ... <div id="green-alert-non-icons-container"></div>'''
        >>> # Assuming report has complete patient, specimen, ordering, run summary, and detection data
        >>> result = populate_html_template(html, report)
        >>> print(result)
        <div id="specimen-type">plasma</div>
        <span id="patient-first-name">John</span>
        <table id="reads-table"><tbody><tr><td>Kaposi sarcoma-associated herpesvirus</td><td>virus</td><td>98.0%</td><td>Disease associated</td><td>98.0%</td><td>1,622</td></tr></tbody></table>
        <div id="red-alert-non-icons-container">
                    <div class="alert-row alert-row-red">
                        <span class="alert-row-organism-type">virus</span>
                        <span class="alert-organism-name">Kaposi sarcoma-associated herpesvirus</span>
                        <img class="alert-light" src="img/red-three-ring.png" alt="alert light">
                    </div>
        </div>
        <div id="yellow-alert-non-icons-container">
                    <div class="alert-row alert-row-yellow">
                        <span class="alert-row-organism-type">bacteria</span>
                        <span class="alert-organism-name">Example bacteria</span>
                        <img class="alert-light" src="img/yellow-one-ring.png" alt="alert light">
                    </div>
        </div>
        <div id="green-alert-non-icons-container">
                    <div class="alert-row alert-row-green">
                        <span class="alert-row-organism-type">parasite</span>
                        <span class="alert-organism-name">Kaposi sarcoma-associated herpesvirus</span>
                        <img class="alert-light" src="img/green-zero-ring.png" alt="alert light">
                    </div>
        </div>
    """
    def replace_content(match: re.Match[str], new_value: str) -> str:
        opening_tag: str = match.group(1)
        closing_tag: str = match.group(3)
        return f"{opening_tag}{new_value}{closing_tag}"

    def create_element_pattern(element_id: str) -> str:
        """Create a regex pattern for finding HTML elements by ID."""
        return rf'(<[^>]+id\s*=\s*["\']{{element_id}}["\'][^>]*>)(.*?)(</[^>]+>)'.format(element_id=element_id)

    def get_alert_light_image(alert_level: AlertLevel, coverage_score: float) -> str:
        """
        Get the appropriate alert light image filename based on alert level and coverage score.

        Args:
            alert_level: The alert level (HIGH=red, MEDIUM=yellow, LOW=green)
            coverage_score: The coverage score (0.0-1.0)

        Returns:
            str: The image filename (e.g., "red-two-ring.png")
        """
        # Determine color based on alert level
        if alert_level == AlertLevel.HIGH:
            color = "red"
        elif alert_level == AlertLevel.MEDIUM:
            color = "yellow"
        else:  # AlertLevel.LOW
            color = "green"

        # Determine number based on coverage score percentage
        coverage_percentage = coverage_score * 100
        if coverage_percentage < 20:
            number = "zero"
        elif coverage_percentage < 61:
            number = "one"
        elif coverage_percentage < 81:
            number = "two"
        else:
            number = "three"

        return f"{color}-{number}-ring.png"

    # Generate all pages dynamically using template classes
    page_1_template = Page1Template(report_html_template, header_html, footer_html)
    page_2_template = Page2Template(report_html_template, header_html, footer_html)
    page_3_template = Page3Template(report_html_template, header_html, footer_html)

    page_1_html = page_1_template.generate_page(report)
    page_2_html = page_2_template.generate_page(report)
    page_3_html = page_3_template.generate_page(report)

    # Insert the generated pages into the template
    pages_html = f"{page_1_html}\n{page_2_html}\n{page_3_html}"
    result_html = re.sub(
        r'(\s*<!-- Pages will be dynamically generated and inserted here -->)',
        pages_html,
        report_html_template,
        flags=re.IGNORECASE
    )

    # Replace specimen-type element content
    specimen_type_value: str = report.specimen.type.value
    result_html = re.sub(create_element_pattern("specimen-type"), lambda m: replace_content(m, specimen_type_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace specimen-id element content
    specimen_id_value: str = str(report.specimen.id)
    result_html = re.sub(create_element_pattern("specimen-id"), lambda m: replace_content(m, specimen_id_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace collection-date element content
    collection_date: CalendarDate = report.specimen.collection_date
    collection_date_value: str = f"{collection_date.year:04d}-{collection_date.month:02d}-{collection_date.day_of_month:02d}"
    result_html = re.sub(create_element_pattern("collection-date"), lambda m: replace_content(m, collection_date_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace patient-first-name element content
    patient_first_name_value: str = report.patient.name.first
    result_html = re.sub(create_element_pattern("patient-first-name"), lambda m: replace_content(m, patient_first_name_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace patient-last-name element content
    patient_last_name_value: str = report.patient.name.last
    result_html = re.sub(create_element_pattern("patient-last-name"), lambda m: replace_content(m, patient_last_name_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace patient-date-of-birth element content
    patient_dob: CalendarDate = report.patient.date_of_birth
    patient_dob_value: str = f"{patient_dob.year:04d}-{patient_dob.month:02d}-{patient_dob.day_of_month:02d}"
    result_html = re.sub(create_element_pattern("patient-date-of-birth"), lambda m: replace_content(m, patient_dob_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace patient-mrn element content
    patient_mrn_value: str = report.patient.mrn
    result_html = re.sub(create_element_pattern("patient-mrn"), lambda m: replace_content(m, patient_mrn_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace ordering-clinician element content
    ordering_person_name: OrderingPersonName = report.ordered.by_person.name
    ordering_clinician_value: str = f"{ordering_person_name.title} {ordering_person_name.first} {ordering_person_name.last}, {ordering_person_name.post_nominal}"
    result_html = re.sub(create_element_pattern("ordering-clinician"), lambda m: replace_content(m, ordering_clinician_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace facility-name element content
    facility_name_value: str = report.ordered.at_facility.display_name
    result_html = re.sub(create_element_pattern("facility-name"), lambda m: replace_content(m, facility_name_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace facility-address-1 element content (street address)
    facility_address_1_value: str = report.ordered.at_facility.address.street
    result_html = re.sub(create_element_pattern("facility-address-1"), lambda m: replace_content(m, facility_address_1_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace facility-address-2 element content (city, state, postal)
    facility_address: PhysicalAddress = report.ordered.at_facility.address
    facility_address_2_value: str = f"{facility_address.locality}, {facility_address.region} {facility_address.postal}"
    result_html = re.sub(create_element_pattern("facility-address-2"), lambda m: replace_content(m, facility_address_2_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace run-status element content
    run_status_value: str = report.run_summary.status
    result_html = re.sub(create_element_pattern("run-status"), lambda m: replace_content(m, run_status_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace reads-processed-meta element content (human-readable format)
    def format_number_human_readable(num: int) -> str:
        if num >= 1_000_000_000:
            formatted = f"{num / 1_000_000_000:.1f}"
            if formatted.endswith('.0'):
                formatted = formatted[:-2]
            return f"{formatted} billion"
        elif num >= 1_000_000:
            formatted = f"{num / 1_000_000:.1f}"
            if formatted.endswith('.0'):
                formatted = formatted[:-2]
            return f"{formatted} million"
        elif num >= 1_000:
            formatted = f"{num / 1_000:.1f}"
            if formatted.endswith('.0'):
                formatted = formatted[:-2]
            return f"{formatted} thousand"
        else:
            return str(num)

    reads_processed_value: str = format_number_human_readable(report.run_summary.total_reads)
    result_html = re.sub(create_element_pattern("reads-processed-meta"), lambda m: replace_content(m, reads_processed_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace software-version element content (vX.Y.Z format)
    software_version: SoftwareVersion = report.run_summary.software_version
    software_version_value: str = f"v{software_version.major}.{software_version.minor}.{software_version.patch}"
    result_html = re.sub(create_element_pattern("software-version"), lambda m: replace_content(m, software_version_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Populate reads-table with detection data
    def generate_detection_rows(detections: set[Detection]) -> str:
        if not detections:
            return '<tr><td colspan="6" style="text-align: center; font-style: italic;">No detections found</td></tr>'

        rows: list[str] = []
        # Sort detections by confidence (highest first) for consistent display
        sorted_detections: list[Detection] = sorted(detections, key=lambda d: d.confidence, reverse=True)

        for detection in sorted_detections:
            confidence_percent: str = f"{detection.confidence:.1%}"
            coverage_percent: str = f"{detection.coverage_score:.1%}"
            soc_count_formatted: str = f"{detection.SoC_count:,}"

            row: str = f"""<tr>
                <td>{detection.reference_genome}</td>
                <td>{detection.type.value}</td>
                <td>{confidence_percent}</td>
                <td>{detection.clinical_significance.value}</td>
                <td>{coverage_percent}</td>
                <td>{soc_count_formatted}</td>
            </tr>"""
            rows.append(row)

        return "".join(rows)

    # Find and replace the tbody content in reads-table
    table_rows: str = generate_detection_rows(report.detections)
    table_pattern: str = r'(<table[^>]*id\s*=\s*["\']reads-table["\'][^>]*>.*?<tbody>)(.*?)(</tbody>.*?</table>)'
    result_html = re.sub(table_pattern, lambda m: f"{m.group(1)}{table_rows}{m.group(3)}", result_html, flags=re.IGNORECASE | re.DOTALL)

    # Generate alert rows for detections with specified alert level
    def generate_alert_rows(detections: set[Detection], alert_level: AlertLevel, css_class: str) -> str:
        rows: list[str] = []

        # Filter detections with specified alert_level and sort by confidence (highest first)
        alert_detections: list[Detection] = sorted(
            [d for d in detections if d.alert_level == alert_level],
            key=lambda d: d.confidence,
            reverse=True
        )

        for detection in alert_detections:
            # Get the appropriate alert light image based on alert level and coverage score
            alert_image: str = get_alert_light_image(detection.alert_level, detection.coverage_score)

            alert_row: str = f"""                    <div class="alert-row {css_class}">
                        <span class="alert-row-organism-type">{detection.type.value}</span>
                        <span class="alert-organism-name">{detection.reference_genome}</span>
                        <img class="alert-light" src="img/{alert_image}" alt="alert light">
                    </div>"""
            rows.append(alert_row)

        return "\n".join(rows)

    # Find and append alert rows to red-alert-non-icons-container
    alert_rows: str = generate_alert_rows(report.detections, AlertLevel.HIGH, "alert-row-red")
    if alert_rows:  # Only modify if there are alert rows to add
        alert_container_pattern: str = r'(<div[^>]*id\s*=\s*["\']red-alert-non-icons-container["\'][^>]*>)(.*?)(</div>)'
        result_html = re.sub(
            alert_container_pattern,
            lambda m: f"{m.group(1)}{m.group(2)}\n{alert_rows}\n{' ' * 20}{m.group(3)}",
            result_html,
            flags=re.IGNORECASE | re.DOTALL
        )

    # Find and append yellow alert rows to yellow-alert-non-icons-container
    yellow_alert_rows: str = generate_alert_rows(report.detections, AlertLevel.MEDIUM, "alert-row-yellow")
    if yellow_alert_rows:  # Only modify if there are yellow alert rows to add
        yellow_alert_container_pattern: str = r'(<div[^>]*id\s*=\s*["\']yellow-alert-non-icons-container["\'][^>]*>)(.*?)(</div>)'
        result_html = re.sub(
            yellow_alert_container_pattern,
            lambda m: f"{m.group(1)}{m.group(2)}\n{yellow_alert_rows}\n{' ' * 20}{m.group(3)}",
            result_html,
            flags=re.IGNORECASE | re.DOTALL
        )

    # Find and append green alert rows to green-alert-non-icons-container
    green_alert_rows: str = generate_alert_rows(report.detections, AlertLevel.LOW, "alert-row-green")
    if green_alert_rows:  # Only modify if there are green alert rows to add
        green_alert_container_pattern: str = r'(<div[^>]*id\s*=\s*["\']green-alert-non-icons-container["\'][^>]*>)(.*?)(</div>)'
        result_html = re.sub(
            green_alert_container_pattern,
            lambda m: f"{m.group(1)}{m.group(2)}\n{green_alert_rows}\n{' ' * 20}{m.group(3)}",
            result_html,
            flags=re.IGNORECASE | re.DOTALL
        )

    # Populate interpretation-list with interpretation notes
    def generate_interpretation_list_items(interpretations: list[InterpretationNote]) -> str:
        if not interpretations:
            return '<li style="font-style: italic;">No interpretations available</li>'

        items: list[str] = []
        for interpretation in interpretations:
            # Apply styling based on the TextStyle enum
            if interpretation.style == TextStyle.BOLD:
                item: str = f'<li><strong>{interpretation.text}</strong></li>'
            else:  # TextStyle.NORMAL
                item = f'<li>{interpretation.text}</li>'
            items.append(item)

        return "\n".join(items)

    # Find and replace the content in interpretation-list
    interpretation_items: str = generate_interpretation_list_items(list(report.interpretations))
    interpretation_list_pattern: str = r'(<ul[^>]*id\s*=\s*["\']interpretation-list["\'][^>]*>)(.*?)(</ul>)'
    result_html = re.sub(
        interpretation_list_pattern,
        lambda m: f"{m.group(1)}\n{interpretation_items}\n{m.group(3)}",
        result_html,
        flags=re.IGNORECASE | re.DOTALL
    )

    # Populate test performance elements
    test_performance: TestPerformance = report.test_performance

    # Replace assay element content
    assay_value: str = test_performance.assay
    result_html = re.sub(create_element_pattern("assay"), lambda m: replace_content(m, assay_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace turnaround-time element content with formatted breakdown
    def format_turnaround_time(turnaround: TurnaroundTime) -> str:
        total_hours: int = turnaround.prep_hours + turnaround.sequencing_hours + turnaround.analysis_hours
        return f"Total time {total_hours} hours ({turnaround.prep_hours} hour prep and extraction, {turnaround.sequencing_hours} hours sequencing, {turnaround.analysis_hours} hour SeqMag analysis)"

    turnaround_time_value: str = format_turnaround_time(test_performance.turnaround_time)
    result_html = re.sub(create_element_pattern("turnaround-time"), lambda m: replace_content(m, turnaround_time_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace limit-of-detection element content
    limit_of_detection_value: str = test_performance.limit_of_detection
    result_html = re.sub(create_element_pattern("limit-of-detection"), lambda m: replace_content(m, limit_of_detection_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace sequencing-platform element content
    sequencing_platform_value: str = test_performance.sequencing_platform
    result_html = re.sub(create_element_pattern("sequencing-platform"), lambda m: replace_content(m, sequencing_platform_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace reads-processed-performance element content
    reads_processed_performance_value: str = test_performance.reads_processed
    result_html = re.sub(create_element_pattern("reads-processed-performance"), lambda m: replace_content(m, reads_processed_performance_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    # Replace software-version-database element content (vX.Y.Z format)
    software_version_database: SoftwareVersion = report.run_summary.software_version
    software_version_database_value: str = f"v{software_version_database.major}.{software_version_database.minor}.{software_version_database.patch}"
    result_html = re.sub(create_element_pattern("software-version-database"), lambda m: replace_content(m, software_version_database_value), result_html, flags=re.IGNORECASE | re.DOTALL)

    return result_html
