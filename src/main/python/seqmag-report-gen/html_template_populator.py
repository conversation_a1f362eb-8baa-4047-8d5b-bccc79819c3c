from __future__ import annotations

import re

from .html_manipulation import replace_html_element_content_by_id
from .report_data import ReportData
from .page1_template import Page1Template
from .page2_template import Page2Template
from .page3_template import Page3Template


def populate_html_template(report_html_template: str, header_html: str, footer_html: str, page1_html: str, page2_html: str, page3_html: str, report: ReportData) -> str:
    """
    Find HTML elements with specific IDs and replace their inner text with corresponding report values.

    Args:
        report_html_template: The main HTML template content (report.html)
        header_html: The header HTML template content (header.html)
        footer_html: The footer HTML template content (footer.html)
        page1_html: The page 1 content HTML (page1.html)
        page2_html: The page 2 content HTML (page2.html)
        page3_html: The page 3 content HTML (page3.html)
        report: The report data to populate into the templates

    Returns:
        str: The populated HTML with all template content and data filled in

    Currently populates:
    - id="specimen-type": Filled with specimen type value (e.g., "plasma", "blood")
    - id="specimen-id": Filled with specimen ID value
    - id="collection-date": Filled with specimen collection date in YYYY-MM-DD format
    - id="patient-first-name": Filled with patient's first name
    - id="patient-last-name": Filled with patient's last name
    - id="patient-date-of-birth": Filled with patient's date of birth in YYYY-MM-DD format
    - id="patient-mrn": Filled with patient's medical record number
    - id="ordering-clinician": Filled with ordering clinician's full name and credentials
    - id="facility-name": Filled with facility display name
    - id="facility-address-1": Filled with facility street address
    - id="facility-address-2": Filled with facility city, state, and postal code
    - id="run-status": Filled with run status (e.g., "success")
    - id="reads-processed-meta": Filled with total reads processed (human-readable format like "120 million")
    - id="software-version": Filled with software version in vX.Y.Z format
    - id="reads-table": Table populated with detection results (tbody filled with detection rows)
    - id="red-alert-non-icons-container": Div populated with alert rows for detections with alert_level HIGH
    - id="yellow-alert-non-icons-container": Div populated with alert rows for detections with alert_level MEDIUM
    - id="green-alert-non-icons-container": Div populated with alert rows for detections with alert_level LOW
    - id="interpretation-list": UL populated with interpretation notes as list items
    - id="assay": Filled with test performance assay type
    - id="turnaround-time": Filled with formatted turnaround time breakdown
    - id="limit-of-detection": Filled with limit of detection information
    - id="sequencing-platform": Filled with sequencing platform information
    - id="reads-processed-performance": Filled with reads processed information
    - id="software-version-database": Filled with software version in vX.Y.Z format

    Args:
        html_template (str): The HTML template content containing elements with specific IDs.
        report (ReportData): The report data containing specimen, analysis, and patient information.

    Returns:
        str: The HTML with element contents replaced by actual report values.

    Example:
        >>> html = '''<div id="specimen-type">placeholder</div>
        ... <span id="patient-first-name">placeholder</span>
        ... <table id="reads-table"><tbody></tbody></table>
        ... <div id="red-alert-non-icons-container"></div>
        ... <div id="yellow-alert-non-icons-container"></div>
        ... <div id="green-alert-non-icons-container"></div>'''
        >>> # Assuming report has complete patient, specimen, ordering, run summary, and detection data
        >>> result = populate_html_template(html, report)
        >>> print(result)
        <div id="specimen-type">plasma</div>
        <span id="patient-first-name">John</span>
        <table id="reads-table"><tbody><tr><td>Kaposi sarcoma-associated herpesvirus</td><td>virus</td><td>98.0%</td><td>Disease associated</td><td>98.0%</td><td>1,622</td></tr></tbody></table>
        <div id="red-alert-non-icons-container">
                    <div class="alert-row alert-row-red">
                        <span class="alert-row-organism-type">virus</span>
                        <span class="alert-organism-name">Kaposi sarcoma-associated herpesvirus</span>
                        <img class="alert-light" src="img/red-three-ring.png" alt="alert light">
                    </div>
        </div>
        <div id="yellow-alert-non-icons-container">
                    <div class="alert-row alert-row-yellow">
                        <span class="alert-row-organism-type">bacteria</span>
                        <span class="alert-organism-name">Example bacteria</span>
                        <img class="alert-light" src="img/yellow-one-ring.png" alt="alert light">
                    </div>
        </div>
        <div id="green-alert-non-icons-container">
                    <div class="alert-row alert-row-green">
                        <span class="alert-row-organism-type">parasite</span>
                        <span class="alert-organism-name">Kaposi sarcoma-associated herpesvirus</span>
                        <img class="alert-light" src="img/green-zero-ring.png" alt="alert light">
                    </div>
        </div>
    """
    def replace_content(match: re.Match[str], new_value: str) -> str:
        opening_tag: str = match.group(1)
        closing_tag: str = match.group(3)
        return f"{opening_tag}{new_value}{closing_tag}"



    # Generate all pages dynamically using template classes
    page_1_template = Page1Template(report_html_template, header_html, footer_html, page1_html)
    page_2_template = Page2Template(report_html_template, header_html, footer_html, page2_html)
    page_3_template = Page3Template(report_html_template, header_html, footer_html, page3_html)

    page_1_html = page_1_template.generate_page(report)
    page_2_html = page_2_template.generate_page(report)
    page_3_html = page_3_template.generate_page(report)

    # Insert the generated pages into the template
    pages_html = f"{page_1_html}\n{page_2_html}\n{page_3_html}"
    result_html = replace_html_element_content_by_id(report_html_template, "report", pages_html)
    return result_html
