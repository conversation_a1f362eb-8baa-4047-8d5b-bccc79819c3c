"""
Page 1 template for HTML report generation.

This module provides the template for the first page of the SeqMag report,
which contains metadata and findings summary.
"""

import re
from .page_template import PageTemplate
from .html_manipulation import replace_html_element_content_by_id, append_html_child_to_element_by_id
from .report_data import ReportData, CalendarDate, OrderingPersonName, PhysicalAddress, SoftwareVersion, Detection, AlertLevel
from .red_alert_container_template import RedAlertContainerTemplate
from .yellow_alert_container_template import Yellow<PERSON>lertContainerTemplate
from .green_alert_container_template import GreenAlertContainerTemplate


class Page1Template(PageTemplate):
    """
    Template for the first page with metadata and findings summary.
    
    This page includes:
    - Specimen information (type, ID, collection date)
    - Patient information (name, DOB, MRN)
    - Order information (clinician, facility)
    - Run summary (status, reads processed, software version)
    - Summary of findings (alert sections)
    - Detection results table
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str, page_content_html: str,
                 red_alert_container_template: str, yellow_alert_container_template: str, green_alert_container_template: str):
        """
        Initialize the Page 1 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
            page_content_html: The page 1 specific content HTML
            red_alert_container_template: The HTML template content for the red alert container
            yellow_alert_container_template: The HTML template content for the yellow alert container
            green_alert_container_template: The HTML template content for the green alert container
        """
        super().__init__(report_html_template, header_html, footer_html, page_content_html, page_number=1)
        self.red_alert_container_template = red_alert_container_template
        self.yellow_alert_container_template = yellow_alert_container_template
        self.green_alert_container_template = green_alert_container_template

    def populate_content(self, report: ReportData) -> str:
        """
        Populate the page 1 content HTML with report data.

        Args:
            report: The report data to use for content population

        Returns:
            str: HTML string with populated page 1 content
        """
        result_html = self.page_content_html

        # Replace specimen-type element content
        specimen_type_value: str = report.specimen.type.value
        result_html = replace_html_element_content_by_id(result_html, "specimen-type", specimen_type_value)

        # Replace specimen-id element content
        specimen_id_value: str = str(report.specimen.id)
        result_html = replace_html_element_content_by_id(result_html, "specimen-id", specimen_id_value)

        # Replace collection-date element content
        collection_date: CalendarDate = report.specimen.collection_date
        collection_date_value: str = f"{collection_date.year:04d}-{collection_date.month:02d}-{collection_date.day_of_month:02d}"
        result_html = replace_html_element_content_by_id(result_html, "collection-date", collection_date_value)

        # Replace patient-first-name element content
        patient_first_name_value: str = report.patient.name.first
        result_html = replace_html_element_content_by_id(result_html, "patient-first-name", patient_first_name_value)

        # Replace patient-last-name element content
        patient_last_name_value: str = report.patient.name.last
        result_html = replace_html_element_content_by_id(result_html, "patient-last-name", patient_last_name_value)

        # Replace patient-date-of-birth element content
        patient_dob: CalendarDate = report.patient.date_of_birth
        patient_dob_value: str = f"{patient_dob.year:04d}-{patient_dob.month:02d}-{patient_dob.day_of_month:02d}"
        result_html = replace_html_element_content_by_id(result_html, "patient-date-of-birth", patient_dob_value)

        # Replace patient-mrn element content
        patient_mrn_value: str = report.patient.mrn
        result_html = replace_html_element_content_by_id(result_html, "patient-mrn", patient_mrn_value)

        # Replace ordering-clinician element content
        ordering_person_name: OrderingPersonName = report.ordered.by_person.name
        ordering_clinician_value: str = f"{ordering_person_name.title} {ordering_person_name.first} {ordering_person_name.last}, {ordering_person_name.post_nominal}"
        result_html = replace_html_element_content_by_id(result_html, "ordering-clinician", ordering_clinician_value)

        # Replace facility-name element content
        facility_name_value: str = report.ordered.at_facility.display_name
        result_html = replace_html_element_content_by_id(result_html, "facility-name", facility_name_value)

        # Replace facility-address-1 element content (street address)
        facility_address_1_value: str = report.ordered.at_facility.address.street
        result_html = replace_html_element_content_by_id(result_html, "facility-address-1", facility_address_1_value)

        # Replace facility-address-2 element content (city, state, postal)
        facility_address: PhysicalAddress = report.ordered.at_facility.address
        facility_address_2_value: str = f"{facility_address.locality}, {facility_address.region} {facility_address.postal}"
        result_html = replace_html_element_content_by_id(result_html, "facility-address-2", facility_address_2_value)

        # Replace run-status element content
        run_status_value: str = report.run_summary.status
        result_html = replace_html_element_content_by_id(result_html, "run-status", run_status_value)

        # Replace reads-processed-meta element content (human-readable format)
        def format_number_human_readable(num: int) -> str:
            if num >= 1_000_000_000:
                formatted = f"{num / 1_000_000_000:.1f}"
                if formatted.endswith('.0'):
                    formatted = formatted[:-2]
                return f"{formatted} billion"
            elif num >= 1_000_000:
                formatted = f"{num / 1_000_000:.1f}"
                if formatted.endswith('.0'):
                    formatted = formatted[:-2]
                return f"{formatted} million"
            elif num >= 1_000:
                formatted = f"{num / 1_000:.1f}"
                if formatted.endswith('.0'):
                    formatted = formatted[:-2]
                return f"{formatted} thousand"
            else:
                return str(num)

        reads_processed_value: str = format_number_human_readable(report.run_summary.total_reads)
        result_html = replace_html_element_content_by_id(result_html, "reads-processed-meta", reads_processed_value)

        # Replace software-version element content (vX.Y.Z format)
        software_version: SoftwareVersion = report.run_summary.software_version
        software_version_value: str = f"v{software_version.major}.{software_version.minor}.{software_version.patch}"
        result_html = replace_html_element_content_by_id(result_html, "software-version", software_version_value)

        # Populate reads-table with detection data
        def generate_detection_rows(detections: set[Detection]) -> str:
            if not detections:
                return '<tr><td colspan="6" style="text-align: center; font-style: italic;">No detections found</td></tr>'

            rows: list[str] = []
            # Sort detections by confidence (highest first) for consistent display
            sorted_detections: list[Detection] = sorted(detections, key=lambda d: d.confidence, reverse=True)

            for detection in sorted_detections:
                confidence_percent: str = f"{detection.confidence:.1%}"
                coverage_percent: str = f"{detection.coverage_score:.1%}"
                soc_count_formatted: str = f"{detection.SoC_count:,}"

                row: str = f"""<tr>
                <td>{detection.reference_genome}</td>
                <td>{detection.type.value}</td>
                <td>{confidence_percent}</td>
                <td>{detection.clinical_significance.value}</td>
                <td>{coverage_percent}</td>
                <td>{soc_count_formatted}</td>
            </tr>"""
                rows.append(row)

            return "".join(rows)

        # Find and replace the tbody content in reads-table
        table_rows: str = generate_detection_rows(report.detections)
        table_pattern: str = r'(<table[^>]*id\s*=\s*["\']reads-table["\'][^>]*>.*?<tbody>)(.*?)(</tbody>.*?</table>)'
        result_html = re.sub(table_pattern, lambda m: f"{m.group(1)}{table_rows}{m.group(3)}", result_html, flags=re.IGNORECASE | re.DOTALL)

        # Generate red alert container using template
        red_alert_container = RedAlertContainerTemplate(self.red_alert_container_template)
        for detection in report.detections:
            if detection.alert_level == AlertLevel.HIGH:
                red_alert_container.add_alert_detection(detection)

        # Generate the populated red alert container HTML
        populated_red_alert_container: str = red_alert_container.generate_populated_container()
        result_html = append_html_child_to_element_by_id(result_html, "summary-of-findings", populated_red_alert_container)

        # Generate yellow alert container using template
        yellow_alert_container = YellowAlertContainerTemplate(self.yellow_alert_container_template)
        for detection in report.detections:
            if detection.alert_level == AlertLevel.MEDIUM:
                yellow_alert_container.add_alert_detection(detection)

        # Generate the populated yellow alert container HTML
        populated_yellow_alert_container: str = yellow_alert_container.generate_populated_container()
        result_html = append_html_child_to_element_by_id(result_html, "summary-of-findings", populated_yellow_alert_container)

        # Generate green alert container using template
        green_alert_container = GreenAlertContainerTemplate(self.green_alert_container_template)
        for detection in report.detections:
            if detection.alert_level == AlertLevel.LOW:
                green_alert_container.add_alert_detection(detection)

        # Generate the populated green alert container HTML
        populated_green_alert_container: str = green_alert_container.generate_populated_container()
        result_html = append_html_child_to_element_by_id(result_html, "summary-of-findings", populated_green_alert_container)

        return result_html
    

