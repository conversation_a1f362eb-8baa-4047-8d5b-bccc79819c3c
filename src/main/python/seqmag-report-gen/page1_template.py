"""
Page 1 template for HTML report generation.

This module provides the template for the first page of the SeqMag report,
which contains metadata and findings summary.
"""

from .page_template import PageTemplate
from .report_data import ReportData


class Page1Template(PageTemplate):
    """
    Template for the first page with metadata and findings summary.
    
    This page includes:
    - Specimen information (type, ID, collection date)
    - Patient information (name, DOB, MRN)
    - Order information (clinician, facility)
    - Run summary (status, reads processed, software version)
    - Summary of findings (alert sections)
    - Detection results table
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str):
        """
        Initialize the Page 1 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
        """
        super().__init__(report_html_template, header_html, footer_html, page_number=1)
    
    def generate_content(self, report: ReportData) -> str:
        """
        Generate the content for page 1.
        
        Args:
            report: The report data (not used in template generation, 
                   but required by base class interface)
            
        Returns:
            str: HTML string for page 1 content
        """
        return '''        <div id="metadata-section">
            <div class="metadata-subsection">
                <div class="metadata-subsection-title">Specimen</div>
                <div class="metadata-subsection-non-title">
                    <div>
                        <span class="metadata-key">Type:</span>
                        <span id="specimen-type" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">ID:</span>
                        <span id="specimen-id" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">Collection Date:</span>
                        <span id="collection-date" class="metadata-value"></span>
                    </div>
                </div>
            </div>
            <div class="metadata-subsection">
                <div class="metadata-subsection-title">Patient</div>
                <div class="metadata-subsection-non-title">
                    <div>
                        <span class="metadata-key">First Name:</span>
                        <span id="patient-first-name" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">Last Name:</span>
                        <span id="patient-last-name" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">Date of Birth:</span>
                        <span id="patient-date-of-birth" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">MRN:</span>
                        <span id="patient-mrn" class="metadata-value"></span>
                    </div>
                </div>
            </div>
            <div class="metadata-subsection">
                <div class="metadata-subsection-title">Order</div>
                <div class="metadata-subsection-non-title">
                    <div>
                        <span class="metadata-key">By:</span>
                        <span id="ordering-clinician" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">Facility:</span>
                        <div id="facility-name" class="metadata-value"></div>
                        <div id="facility-address-1" class="metadata-value"></div>
                        <div id="facility-address-2" class="metadata-value"></div>
                    </div>
                </div>
            </div>
            <div class="metadata-subsection">
                <div class="metadata-subsection-title">SeqMag Run Summary</div>
                <div class="metadata-subsection-non-title">
                    <div>
                        <span class="metadata-key">Run Status:</span>
                        <span id="run-status" class="metadata-value green-metadata-value">Successful</span>
                    </div>
                    <div>
                        <span class="metadata-key">Total Reads Processed:</span>
                        <span id="reads-processed-meta" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">Software Version:</span>
                        <span id="software-version" class="metadata-value"></span>
                    </div>
                    <div>
                        <span class="metadata-key">FDA Status:</span>
                        <span class="metadata-value">Investigational Use Only</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="section">
            <div class="section-title">Summary of Findings</div>
            <div id="red-alert" class="alert-container">
                <div id="red-alert-non-icons-container" class="alert-non-icons-container">
                    <span class="alert-title-major-text">⚠ Obligate & Opportunist Pathogens</span>
                    <span class="alert-title-minor-text">Likely to cause disease in humans at any quantity</span>
                </div>
            </div>
            <div id="yellow-alert" class="alert-container">
                <div id="yellow-alert-non-icons-container" class="alert-non-icons-container">
                    <span class="alert-title-major-text">⚠ Commensal Pathogens & DNA Viruses</span>
                    <span class="alert-title-minor-text">Known to be associated with disease but may also represent normal
                                                     microbiota</span>
                </div>
            </div>
            <div id="green-alert" class="alert-container">
                <div id="green-alert-non-icons-container" class="alert-non-icons-container">
                    <span class="alert-title-major-text">✓ Low Priority Detections</span>
                    <span class="alert-title-minor-text">Detected organisms with low clinical significance</span>
                </div>
            </div>
        </div>
        <table id="reads-table">
            <thead>
            <tr>
                <th>Reference Genome</th>
                <th>Type</th>
                <th>Confidence</th>
                <th>Clinical Significance</th>
                <th>CovScore</th>
                <th>SoC Count</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
        </table>'''
