from __future__ import annotations

from dataclasses import dataclass
from enum import Enum
from typing import Mapping, Sequence, Union, Set
from uuid import UUID

JSONScalar = Union[str, int, float, bool, None]
"""Type alias for JSON scalar values (string, number, boolean, or null)."""

JSONValue = Union[JSONScalar, "JSONArray", "JSONObject"]
"""Type alias for any valid JSON value (scalar, array, or object)."""

JSONArray = Sequence[JSONValue]
"""Type alias for JSON arrays (sequences of JSON values)."""

JSONObject = Mapping[str, JSONValue]
"""Type alias for JSON objects (mappings from strings to JSON values)."""


class SpecimenType(Enum):
    """
    Enumeration of valid specimen types for analysis.

    Values:
        PLASMA: Plasma specimen type
        BLOOD: Blood specimen type
    """
    PLASMA = "plasma"
    BLOOD = "blood"


class PathogenType(Enum):
    """
    Enumeration of valid pathogen types.

    Values:
        BACTERIA: Bacterial pathogen
        VIRUS: Viral pathogen
        FUNGUS: Fungal pathogen
        PARASITE: Parasitic pathogen
    """
    BACTERIA = "bacteria"
    VIRUS = "virus"
    FUNGUS = "fungus"
    PARASITE = "parasite"


class ClinicalSignificance(Enum):
    """
    Enumeration of valid clinical significance values.

    Values:
        DISEASE_ASSOCIATED: Disease associated pathogen
        COMMENSAL_LAB_STRAIN: Commensal or laboratory strain
    """
    DISEASE_ASSOCIATED = "Disease associated"
    COMMENSAL_LAB_STRAIN = "Commensal/lab strain"


class AlertLevel(Enum):
    """
    Enumeration of alert levels for pathogen detections.

    Values:
        HIGH: High priority alert requiring immediate attention.
        MEDIUM: Medium priority alert requiring attention.
        LOW: Low priority alert, normal detection.
    """
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TextStyle(Enum):
    """
    Enumeration of text styles.

    Values:
        NORMAL: Normal text style.
        BOLD: Bold text style.
    """
    NORMAL = "normal"
    BOLD = "bold"


@dataclass(frozen=True)
class CalendarDate:
    """
    Immutable representation of a calendar date.

    Attributes:
        year (int): The year.
        month (int): The month (1-12).
        day_of_month (int): The day of the month (1-31).
    """
    year: int
    month: int
    day_of_month: int


@dataclass(frozen=True)
class PatientName:
    """
    Immutable representation of a patient's name.

    Attributes:
        first (str): The patient's first name.
        last (str): The patient's last name.
    """
    first: str
    last: str


@dataclass(frozen=True)
class Patient:
    """
    Immutable representation of a patient.

    Attributes:
        name (PatientName): The patient's name information.
        date_of_birth (CalendarDate): The patient's date of birth.
        mrn (str): The patient's medical record number.
    """
    name: PatientName
    date_of_birth: CalendarDate
    mrn: str


@dataclass(frozen=True)
class OrderingPersonName:
    """
    Immutable representation of an ordering person's name with title and credentials.

    Attributes:
        first (str): The person's first name.
        last (str): The person's last name.
        title (str): The person's title (e.g., "Dr.", "Prof.").
        post_nominal (str): The person's post-nominal credentials (e.g., "M.D.", "Ph.D.").
    """
    first: str
    last: str
    title: str
    post_nominal: str


@dataclass(frozen=True)
class OrderingPerson:
    """
    Immutable representation of the person who ordered the test.

    Attributes:
        name (OrderingPersonName): The ordering person's name and credentials.
    """
    name: OrderingPersonName


@dataclass(frozen=True)
class PhysicalAddress:
    """
    Immutable representation of a physical address.

    Attributes:
        street (str): The street address.
        locality (str): The city or locality.
        region (str): The state, province, or region.
        postal (str): The postal or ZIP code.
    """
    street: str
    locality: str
    region: str
    postal: str


@dataclass(frozen=True)
class Facility:
    """
    Immutable representation of a medical facility.

    Attributes:
        display_name (str): The facility's display name.
        address (PhysicalAddress): The facility's address information.
    """
    display_name: str
    address: PhysicalAddress


@dataclass(frozen=True)
class Ordered:
    """
    Immutable representation of ordering information for the test.

    Attributes:
        by_person (OrderingPerson): The person who ordered the test.
        at_facility (Facility): The facility where the test was ordered.
    """
    by_person: OrderingPerson
    at_facility: Facility


@dataclass(frozen=True)
class SoftwareVersion:
    """
    Immutable representation of software version information.

    Attributes:
        major (int): The major version number.
        minor (int): The minor version number.
        patch (int): The patch version number.
    """
    major: int
    minor: int
    patch: int


@dataclass(frozen=True)
class RunSummary:
    """
    Immutable representation of sequencing run summary information.

    Attributes:
        status (str): The status of the run (e.g., "success", "failed").
        total_reads (int): The total number of reads in the run.
        software_version (SoftwareVersion): The version of software used for analysis.
        mapped_reads (int): The number of reads that were successfully mapped.
        mapped_reads_percentage (float): The percentage of reads that were mapped (0.0 to 1.0).
    """
    status: str
    total_reads: int
    software_version: SoftwareVersion
    mapped_reads: int
    mapped_reads_percentage: float


@dataclass(frozen=True)
class Detection:
    """
    Immutable representation of a pathogen detection result.

    Attributes:
        reference_genome (str): The name of the reference genome detected.
        type (PathogenType): The type of pathogen (bacteria, virus, or fungus).
        confidence (float): The confidence score for the detection (0.0 to 1.0).
        clinical_significance (ClinicalSignificance): The clinical significance of the detection.
        coverage_score (float): The coverage score for the detection (0.0 to 1.0).
        SoC_count (int): The Strength of Confidence count.
        alert_level (AlertLevel): The alert level for the detection (high, medium, or low).
    """
    reference_genome: str
    type: PathogenType
    confidence: float
    clinical_significance: ClinicalSignificance
    coverage_score: float
    SoC_count: int
    alert_level: AlertLevel


@dataclass(frozen=True)
class Specimen:
    """
    Immutable representation of a specimen used in analysis.

    Attributes:
        type (SpecimenType): The type of specimen (plasma or blood).
        id (str): The unique identifier for this specimen.
        collection_date (CalendarDate): The date when the specimen was collected.
    """
    type: SpecimenType
    id: str
    collection_date: CalendarDate


@dataclass(frozen=True)
class InterpretationNote:
    """
    Immutable representation of an interpretation note with styling.

    Attributes:
        text (str): The interpretation text content.
        style (TextStyle): The style for displaying the text (normal or bold).
    """
    text: str
    style: TextStyle


@dataclass(frozen=True)
class TurnaroundTime:
    """
    Immutable representation of test turnaround time breakdown.

    Attributes:
        prep_hours (int): Hours required for sample preparation.
        sequencing_hours (int): Hours required for sequencing.
        analysis_hours (int): Hours required for analysis.
    """
    prep_hours: int
    sequencing_hours: int
    analysis_hours: int


@dataclass(frozen=True)
class TestPerformance:
    """
    Immutable representation of test performance information.

    Attributes:
        assay (str): The type of assay performed.
        turnaround_time (TurnaroundTime): The breakdown of time required for different phases.
        limit_of_detection (str): The limit of detection for the assay.
        sequencing_platform (str): The sequencing platform used.
        reads_processed (str): Description of reads processed.
    """
    assay: str
    turnaround_time: TurnaroundTime
    limit_of_detection: str
    sequencing_platform: str
    reads_processed: str


@dataclass(frozen=True)
class ReportData:
    """
    Immutable wrapper for parsed JSON report data conforming to specification version 0.1.

    This class represents a validated report that has been parsed from JSON and confirmed
    to match the expected specification format. The class name indicates compatibility
    with Version Major=0, Minor=1, Patch=X (any patch version).

    The class is frozen (immutable) to ensure data integrity and thread safety.

    Attributes:
        analysis_id (UUID): Unique identifier for this analysis, extracted from the JSON
                           'analysis.id' field and validated as a proper UUID.
        specimen (Specimen): The specimen information including type, id, and collection date.
        patient (Patient): The patient information including name, date of birth, and MRN.
        ordered (Ordered): The ordering information including ordering person and facility.
        run_summary (RunSummary): The sequencing run summary including status, reads, and software version.
        detections (Set[Detection]): The set of pathogen detections found in the analysis.
        interpretations (Sequence[InterpretationNote]): The sequence of interpretation notes with styling.
        test_performance (TestPerformance): The test performance information including assay type and turnaround time.
    """
    analysis_id: UUID
    specimen: Specimen
    patient: Patient
    ordered: Ordered
    run_summary: RunSummary
    detections: Set[Detection]
    interpretations: Sequence[InterpretationNote]
    test_performance: TestPerformance