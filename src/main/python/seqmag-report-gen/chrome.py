from __future__ import annotations

import shlex
import subprocess
import sys
from pathlib import Path



def run_chrome_pdf_generation(output_pdf_path: Path, input_html_path: Path) -> None:
    """
    Build and run the headless Chrome command to generate PDF from HTML.

    Assumes 'google-chrome' is on PATH. Builds the command with appropriate
    arguments and executes it, raising an exception on non-zero exit.

    Args:
        output_pdf_path (Path): Path where the generated PDF will be saved.
        input_html_path (Path): Path to the HTML file to convert.

    Raises:
        FileNotFoundError: If the input HTML file does not exist.
        FileExistsError: If the output PDF file already exists.
        subprocess.CalledProcessError: If Chrome returns a non-zero exit code.
    """
    if not input_html_path.exists():
        raise FileNotFoundError(f"Input HTML file not found: {input_html_path}")

    if output_pdf_path.exists():
        raise FileExistsError(f"Output PDF already exists: {output_pdf_path}")

    cmd: list[str] = [
        "google-chrome",
        "--headless", # Run without opening browser window
        "--disable-gpu", # Disable GPU to avoid errors on headless systems
        f"--print-to-pdf={str(output_pdf_path)}",
        "--no-pdf-header-footer", # Do not include annoying header/footer text browsers add to a page when printing
        "--run-all-compositor-stages-before-draw", # ensure full render of page before pdf generation starts
        input_html_path.as_uri(),
    ]
    print("Running:", " ".join(shlex.quote(c) for c in cmd))
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}: {' '.join(shlex.quote(c) for c in cmd)}", file=sys.stderr)
        raise
