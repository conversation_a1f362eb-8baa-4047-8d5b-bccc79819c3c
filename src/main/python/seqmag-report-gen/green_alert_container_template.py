"""
Green alert container template for HTML report generation.

This module provides functionality to generate and populate green alert containers
in the SeqMag report, specifically handling low-level alert detections.
"""

import re
from .page_template import get_alert_light_image
from .report_data import Detection, AlertLevel


class GreenAlertContainerTemplate:
    """
    Template class for generating green alert container content.

    This class handles the generation of green alert rows for low-level detections
    and manages the green alert container template content.
    """

    def __init__(self, container_template: str):
        """
        Initialize the GreenAlertContainerTemplate.

        Args:
            container_template: The HTML template content for the green alert container
        """
        self.container_template = container_template
        self.alert_rows: list[str] = []
    
    def add_alert_detection(self, detection: Detection) -> None:
        """
        Add a single alert detection to the container.

        Only adds the detection if it has a LOW alert level. Generates the HTML
        row for the detection and adds it to the internal list of alert rows.

        Args:
            detection: Detection object to add (must have LOW alert level)
        """
        # Only process LOW alert level detections
        if detection.alert_level != AlertLevel.LOW:
            raise ValueError(f"Detection {detection} does not have LOW alert level")

        # Get the appropriate alert light image based on alert level and coverage score
        alert_image: str = get_alert_light_image(detection.alert_level, detection.coverage_score)

        alert_row: str = f"""                    <div class="alert-row alert-row-green">
                        <span class="alert-row-organism-type">{detection.type.value}</span>
                        <span class="alert-organism-name">{detection.reference_genome}</span>
                        <img class="alert-light" src="img/{alert_image}" alt="alert light">
                    </div>"""

        self.alert_rows.append(alert_row)
    
    def generate_populated_container(self) -> str:
        """
        Generate the complete populated green alert container HTML.

        Takes the container template and inserts the stored alert rows into it.
        Uses regex to locate the green-alert-non-icons-container element and
        populate it with the alert rows.

        Returns:
            str: Complete HTML for the populated green alert container
        """
        # Get the stored alert rows
        alert_rows: str = "\n".join(self.alert_rows)

        # Start with the container template
        populated_container = self.container_template

        # Only modify if there are alert rows to add
        if alert_rows:
            alert_container_pattern: str = r'(<div[^>]*id\s*=\s*["\']green-alert-non-icons-container["\'][^>]*>)(.*?)(</div>)'
            populated_container = re.sub(
                alert_container_pattern,
                lambda m: f"{m.group(1)}{m.group(2)}\n{alert_rows}\n{' ' * 20}{m.group(3)}",
                populated_container,
                flags=re.IGNORECASE | re.DOTALL
            )

        return populated_container
