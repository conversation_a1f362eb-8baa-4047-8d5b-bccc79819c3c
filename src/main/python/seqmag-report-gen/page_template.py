"""
Base page template class for HTML report generation.

This module provides the abstract base class for all page templates used in
the SeqMag report generation system.
"""

from abc import ABC, abstractmethod
from .report_data import ReportData


class PageTemplate(ABC):
    """
    Abstract base class for page templates.

    This class provides the common structure and functionality for all page templates,
    including standard header and footer generation, while requiring subclasses to
    implement their specific content generation logic.

    Attributes:
        report_html_template (str): The HTML template string for this page
        header_html (str): The header HTML template content
        footer_html (str): The footer HTML template content
        page_number (int): The page number for this template
        total_pages (int): The total number of pages in the report
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str, page_number: int, total_pages: int = 3):
        """
        Initialize the page template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
            page_number: The page number for this template
            total_pages: The total number of pages in the report (default: 3)
        """
        self.report_html_template = report_html_template
        self.header_html = header_html
        self.footer_html = footer_html
        self.page_number = page_number
        self.total_pages = total_pages
    
    def generate_header(self) -> str:
        """
        Generate the standard page header HTML.

        Uses the header HTML content provided during initialization.

        Returns:
            str: HTML string for the page header with logo and title
        """
        return self.header_html
    
    def generate_footer(self) -> str:
        """
        Generate the standard page footer HTML.

        Uses the footer HTML content provided during initialization and
        replaces placeholders with actual page numbers.

        Returns:
            str: HTML string for the page footer with website link and page numbers
        """
        # Replace placeholders with actual values
        footer_html = self.footer_html.replace('{{PAGE_NUMBER}}', str(self.page_number))
        footer_html = footer_html.replace('{{TOTAL_PAGES}}', str(self.total_pages))
        return footer_html
    
    @abstractmethod
    def generate_content(self, report: ReportData) -> str:
        """
        Generate the page-specific content HTML.
        
        This method must be implemented by subclasses to provide the specific
        content for each page type.
        
        Args:
            report: The report data to use for content generation
            
        Returns:
            str: HTML string for the page-specific content
            
        Raises:
            NotImplementedError: If not implemented by subclass
        """
        pass
    
    def generate_page(self, report: ReportData) -> str:
        """
        Generate the complete page HTML.
        
        Combines the header, page-specific content, and footer into a complete
        page HTML structure.
        
        Args:
            report: The report data to use for content generation
            
        Returns:
            str: Complete HTML string for the page
        """
        header = self.generate_header()
        content = self.generate_content(report)
        footer = self.generate_footer()
        
        return f'''    <div class="page">
{header}
{content}
{footer}
    </div>'''
