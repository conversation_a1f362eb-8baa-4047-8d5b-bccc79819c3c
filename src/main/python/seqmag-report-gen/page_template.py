"""
Base page template class for HTML report generation.

This module provides the abstract base class for all page templates used in
the SeqMag report generation system.
"""
from .html_manipulation import replace_html_element_content_by_class
from .report_data import ReportData, AlertLevel

def get_alert_light_image(alert_level: AlertLevel, coverage_score: float) -> str:
    """
    Get the appropriate alert light image filename based on alert level and coverage score.

    Args:
        alert_level: The alert level (HIGH=red, MEDIUM=yellow, LOW=green)
        coverage_score: The coverage score (0.0-1.0)

    Returns:
        str: The image filename (e.g., "red-two-ring.png")
    """
    # Determine color based on alert level
    if alert_level == AlertLevel.HIGH:
        color = "red"
    elif alert_level == AlertLevel.MEDIUM:
        color = "yellow"
    else:  # AlertLevel.LOW
        color = "green"

    # Determine number based on coverage score percentage
    coverage_percentage = coverage_score * 100
    if coverage_percentage < 20:
        number = "zero"
    elif coverage_percentage < 61:
        number = "one"
    elif coverage_percentage < 81:
        number = "two"
    else:
        number = "three"

    return f"{color}-{number}-ring.png"


class PageTemplate:
    """
    Abstract base class for page templates.

    This class provides the common structure and functionality for all page templates,
    including standard header and footer generation, while requiring subclasses to
    implement their specific content generation logic.

    Attributes:
        report_html_template (str): The HTML template string for this page
        header_html (str): The header HTML template content
        footer_html (str): The footer HTML template content
        page_content_html (str): The page-specific content HTML
        page_number (int): The page number for this template
        total_pages (int): The total number of pages in the report
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str, page_content_html: str, page_number: int, total_pages: int = 3):
        """
        Initialize the page template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
            page_content_html: The page-specific content HTML
            page_number: The page number for this template
            total_pages: The total number of pages in the report (default: 3)
        """
        self.report_html_template = report_html_template
        self.header_html = header_html
        self.footer_html = footer_html
        self.page_content_html = page_content_html
        self.page_number = page_number
        self.total_pages = total_pages
    
    def generate_header(self) -> str:
        """
        Generate the standard page header HTML.

        Uses the header HTML content provided during initialization.

        Returns:
            str: HTML string for the page header with logo and title
        """
        return self.header_html
    
    def generate_footer(self) -> str:
        """
        Generate the standard page footer HTML.

        Uses the footer HTML content provided during initialization and
        replaces placeholders with actual page numbers.

        Returns:
            str: HTML string for the page footer with website link and page numbers
        """

        page_number = str(self.page_number) + "/" + str(self.total_pages)
        footer_html = replace_html_element_content_by_class(self.footer_html, "page-number", page_number)
        return footer_html
    
    def populate_content(self, report: ReportData) -> str:
        """
        Populate the page content HTML with report data.

        This method should be overridden by subclasses to implement
        page-specific data population logic.

        Args:
            report: The report data to use for content population

        Returns:
            str: HTML string with populated content
        """
        return self.page_content_html

    def generate_content(self, report: ReportData) -> str:
        """
        Generate the page-specific content HTML.

        Calls populate_content to allow subclasses to customize data population.

        Args:
            report: The report data to use for content generation

        Returns:
            str: HTML string for the page-specific content
        """
        return self.populate_content(report)
    
    def generate_page(self, report: ReportData) -> str:
        """
        Generate the complete page HTML.
        
        Combines the header, page-specific content, and footer into a complete
        page HTML structure.
        
        Args:
            report: The report data to use for content generation
            
        Returns:
            str: Complete HTML string for the page
        """
        header = self.generate_header()
        content = self.generate_content(report)
        footer = self.generate_footer()
        
        return f'''    <div class="page">
{header}
{content}
{footer}
    </div>'''
