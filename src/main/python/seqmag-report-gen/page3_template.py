"""
Page 3 template for HTML report generation.

This module provides the template for the third page of the SeqMag report,
which contains clinical consultation information.
"""

from .page_template import PageTemplate
from .report_data import ReportData


class Page3Template(PageTemplate):
    """
    Template for the third page with clinical consultation information.
    
    This page includes:
    - Clinical consultation contact information
    - Email and address details for support team
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str):
        """
        Initialize the Page 3 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
        """
        super().__init__(report_html_template, header_html, footer_html, page_number=3)
    
    def generate_content(self, report: ReportData) -> str:
        """
        Generate the content for page 3.
        
        Args:
            report: The report data (not used in template generation, 
                   but required by base class interface)
            
        Returns:
            str: HTML string for page 3 content
        """
        return '''        <div class="section">
            <div class="section-title">For Clinical Consultation</div>
            <div class="section-non-title">
                <div class="consultation-row">
                    For result interpretation or to discuss patient findings, contact our infectious disease support team:
                </div>
                <div class="consultation-row">
                    <span>Email:</span>
                    <a href="mailto:<EMAIL>" id="consultation-email"><EMAIL></a>
                    <span>| Address:</span>
                    <span>Rice Nexus, Suite 325, 3rd Floor, Ion Houston</span>
                </div>
            </div>
        </div>'''
