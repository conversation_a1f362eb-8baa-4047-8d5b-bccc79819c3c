"""
Page 3 template for HTML report generation.

This module provides the template for the third page of the SeqMag report,
which contains clinical consultation information.
"""

from .page_template import PageTemplate


class Page3Template(PageTemplate):
    """
    Template for the third page with clinical consultation information.
    
    This page includes:
    - Clinical consultation contact information
    - Email and address details for support team
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str, page_content_html: str):
        """
        Initialize the Page 3 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
            page_content_html: The page 3 specific content HTML
        """
        super().__init__(report_html_template, header_html, footer_html, page_content_html, page_number=3)
    

