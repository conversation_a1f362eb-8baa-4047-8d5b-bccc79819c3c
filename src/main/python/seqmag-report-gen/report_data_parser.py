"""
JSON parsing module for SeqMag report data.

This module provides functions to parse and validate JSON report data
into structured ReportData objects.
"""

import json
from pathlib import Path
from typing import Any, Dict, List, Mapping, Sequence, Union, TextIO
from uuid import UUID

from .report_data import (
    ReportData, Specimen, Patient, Ordered, RunSummary, Detection,
    InterpretationNote, TestPerformance, CalendarDate, PatientName,
    OrderingPersonName, OrderingPerson, Facility, PhysicalAddress,
    SoftwareVersion, TurnaroundTime, SpecimenType, PathogenType,
    ClinicalSignificance, AlertLevel, TextStyle
)

# Type alias for JSON values
JSONValue = Union[str, int, float, bool, None, Dict[str, Any], List[Any]]


def parse_report_json(json_file: TextIO) -> ReportData:
    """
    Parse a JSON file containing SeqMag report data.

    Args:
        json_file: Text file object containing JSON data

    Returns:
        ReportData: Parsed and validated report data

    Raises:
        json.JSONDecodeError: If the file contains invalid JSON
        ValueError: If the JSON structure or values are invalid
    """
    try:
        json_data = json.load(json_file)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Invalid JSON: {e.msg}", e.doc, e.pos)

    return make_report_data_from_json(json_data)


def make_report_data_from_json(json_data: JSONValue) -> ReportData:
    """
    Parse JSON data into a ReportData object.

    Validates the specification target version and parses all required fields
    into a structured ReportData object.

    Args:
        json_data: JSON data as a dictionary

    Returns:
        ReportData: Parsed and validated report data

    Raises:
        ValueError: If the JSON structure or values are invalid, including:
            - Missing or invalid specification_target_version
            - Unsupported specification version
            - Missing required fields
            - Invalid field types or enum values
    """
    if not isinstance(json_data, Mapping):
        raise ValueError("JSON root must be an object")

    # Validate specification target version
    _validate_specification_target_version(json_data)

    # Parse each section
    analysis_id = _parse_analysis(json_data)
    specimen = _parse_specimen(json_data)
    patient = _parse_patient(json_data)
    ordered = _parse_ordered(json_data)
    run_summary = _parse_run_summary(json_data)
    detections = _parse_detections(json_data)
    interpretations = _parse_interpretations(json_data)
    test_performance = _parse_test_performance(json_data)

    return ReportData(
        analysis_id=analysis_id,
        specimen=specimen,
        patient=patient,
        ordered=ordered,
        run_summary=run_summary,
        detections=set(detections),
        interpretations=interpretations,
        test_performance=test_performance
    )


def _validate_specification_target_version(data: Mapping[str, JSONValue]) -> None:
    """
    Validate the specification_target_version field.

    Args:
        data: JSON data containing the specification_target_version field

    Raises:
        ValueError: If the specification version is missing, invalid, or unsupported
    """
    spec_version_data = data.get("specification_target_version")
    if not isinstance(spec_version_data, Mapping):
        raise ValueError("JSON must contain a 'specification_target_version' object")

    # Parse major and minor version
    major = spec_version_data.get("major")
    minor = spec_version_data.get("minor")

    if not isinstance(major, int):
        raise ValueError("'specification_target_version.major' must be an integer")
    if not isinstance(minor, int):
        raise ValueError("'specification_target_version.minor' must be an integer")

    # Check if this is a supported version
    # Currently we only support version 0.1
    if major != 0 or minor != 1:
        raise ValueError(f"Unsupported specification version {major}.{minor}. Supported versions: 0.1")


def _parse_analysis(data: Mapping[str, JSONValue]) -> UUID:
    """Parse analysis section and return the analysis ID."""
    analysis_data = data.get("analysis")
    if not isinstance(analysis_data, Mapping):
        raise ValueError("JSON must contain an 'analysis' object")

    analysis_id = analysis_data.get("id")
    if not isinstance(analysis_id, str):
        raise ValueError("'analysis.id' must be a string")

    try:
        uuid_id = UUID(analysis_id)
    except ValueError:
        raise ValueError(f"'analysis.id' must be a valid UUID, got: {analysis_id}")

    return uuid_id


def _parse_specimen(data: Mapping[str, JSONValue]) -> Specimen:
    """Parse specimen section."""
    specimen_data = data.get("specimen")
    if not isinstance(specimen_data, Mapping):
        raise ValueError("JSON must contain a 'specimen' object")
    
    # Parse specimen type
    specimen_type = specimen_data.get("type")
    if not isinstance(specimen_type, str):
        raise ValueError("'specimen.type' must be a string")
    
    try:
        type_enum = SpecimenType(specimen_type)
    except ValueError:
        valid_types = [t.value for t in SpecimenType]
        raise ValueError(f"'specimen.type' must be one of {valid_types}, got: {specimen_type}")
    
    # Parse specimen ID
    specimen_id = specimen_data.get("id")
    if not isinstance(specimen_id, str):
        raise ValueError("'specimen.id' must be a string")
    
    # Parse collection date
    collection_date = _parse_calendar_date(specimen_data, "collection_date")
    
    return Specimen(type=type_enum, id=specimen_id, collection_date=collection_date)


def _parse_patient(data: Mapping[str, JSONValue]) -> Patient:
    """Parse patient section."""
    patient_data = data.get("patient")
    if not isinstance(patient_data, Mapping):
        raise ValueError("JSON must contain a 'patient' object")
    
    # Parse patient name
    name_data = patient_data.get("name")
    if not isinstance(name_data, Mapping):
        raise ValueError("JSON must contain a 'patient.name' object")
    
    first_name = name_data.get("first")
    last_name = name_data.get("last")
    
    if not isinstance(first_name, str):
        raise ValueError("'patient.name.first' must be a string")
    if not isinstance(last_name, str):
        raise ValueError("'patient.name.last' must be a string")
    
    patient_name = PatientName(first=first_name, last=last_name)
    
    # Parse date of birth
    date_of_birth = _parse_calendar_date(patient_data, "date_of_birth")
    
    # Parse MRN
    mrn = patient_data.get("mrn")
    if not isinstance(mrn, str):
        raise ValueError("'patient.mrn' must be a string")
    
    return Patient(name=patient_name, date_of_birth=date_of_birth, mrn=mrn)


def _parse_calendar_date(data: Mapping[str, JSONValue], field_name: str) -> CalendarDate:
    """Parse a calendar date object."""
    date_data = data.get(field_name)
    if not isinstance(date_data, Mapping):
        raise ValueError(f"JSON must contain a '{field_name}' object")
    
    year = date_data.get("year")
    month = date_data.get("month")
    day_of_month = date_data.get("day_of_month")
    
    if not isinstance(year, int):
        raise ValueError(f"'{field_name}.year' must be an integer")
    if not isinstance(month, int):
        raise ValueError(f"'{field_name}.month' must be an integer")
    if not isinstance(day_of_month, int):
        raise ValueError(f"'{field_name}.day_of_month' must be an integer")
    
    return CalendarDate(year=year, month=month, day_of_month=day_of_month)


def _parse_ordered(data: Mapping[str, JSONValue]) -> Ordered:
    """Parse ordered section."""
    ordered_data = data.get("ordered")
    if not isinstance(ordered_data, Mapping):
        raise ValueError("JSON must contain an 'ordered' object")
    
    # Parse by_person
    by_person_data = ordered_data.get("by_person")
    if not isinstance(by_person_data, Mapping):
        raise ValueError("JSON must contain an 'ordered.by_person' object")
    
    # Parse ordering person name
    name_data = by_person_data.get("name")
    if not isinstance(name_data, Mapping):
        raise ValueError("JSON must contain an 'ordered.by_person.name' object")
    
    first = name_data.get("first")
    last = name_data.get("last")
    title = name_data.get("title")
    post_nominal = name_data.get("post_nominal")
    
    if not isinstance(first, str):
        raise ValueError("'ordered.by_person.name.first' must be a string")
    if not isinstance(last, str):
        raise ValueError("'ordered.by_person.name.last' must be a string")
    if not isinstance(title, str):
        raise ValueError("'ordered.by_person.name.title' must be a string")
    if not isinstance(post_nominal, str):
        raise ValueError("'ordered.by_person.name.post_nominal' must be a string")
    
    ordering_name = OrderingPersonName(first=first, last=last, title=title, post_nominal=post_nominal)
    ordering_person = OrderingPerson(name=ordering_name)
    
    # Parse at_facility
    facility_data = ordered_data.get("at_facility")
    if not isinstance(facility_data, Mapping):
        raise ValueError("JSON must contain an 'ordered.at_facility' object")
    
    display_name = facility_data.get("display_name")
    if not isinstance(display_name, str):
        raise ValueError("'ordered.at_facility.display_name' must be a string")
    
    # Parse facility address
    address_data = facility_data.get("address")
    if not isinstance(address_data, Mapping):
        raise ValueError("JSON must contain an 'ordered.at_facility.address' object")
    
    street = address_data.get("street")
    locality = address_data.get("locality")
    region = address_data.get("region")
    postal = address_data.get("postal")
    
    if not isinstance(street, str):
        raise ValueError("'ordered.at_facility.address.street' must be a string")
    if not isinstance(locality, str):
        raise ValueError("'ordered.at_facility.address.locality' must be a string")
    if not isinstance(region, str):
        raise ValueError("'ordered.at_facility.address.region' must be a string")
    if not isinstance(postal, str):
        raise ValueError("'ordered.at_facility.address.postal' must be a string")
    
    address = PhysicalAddress(street=street, locality=locality, region=region, postal=postal)
    facility = Facility(display_name=display_name, address=address)
    
    return Ordered(by_person=ordering_person, at_facility=facility)


def _parse_run_summary(data: Mapping[str, JSONValue]) -> RunSummary:
    """Parse run_summary section."""
    run_data = data.get("run_summary")
    if not isinstance(run_data, Mapping):
        raise ValueError("JSON must contain a 'run_summary' object")
    
    status = run_data.get("status")
    total_reads = run_data.get("total_reads")
    mapped_reads = run_data.get("mapped_reads")
    mapped_reads_percentage = run_data.get("mapped_reads_percentage")
    
    if not isinstance(status, str):
        raise ValueError("'run_summary.status' must be a string")
    if not isinstance(total_reads, int):
        raise ValueError("'run_summary.total_reads' must be an integer")
    if not isinstance(mapped_reads, int):
        raise ValueError("'run_summary.mapped_reads' must be an integer")
    if not isinstance(mapped_reads_percentage, (int, float)):
        raise ValueError("'run_summary.mapped_reads_percentage' must be a number")
    
    # Parse software version
    version_data = run_data.get("software_version")
    if not isinstance(version_data, Mapping):
        raise ValueError("JSON must contain a 'run_summary.software_version' object")
    
    major = version_data.get("major")
    minor = version_data.get("minor")
    patch = version_data.get("patch")
    
    if not isinstance(major, int):
        raise ValueError("'run_summary.software_version.major' must be an integer")
    if not isinstance(minor, int):
        raise ValueError("'run_summary.software_version.minor' must be an integer")
    if not isinstance(patch, int):
        raise ValueError("'run_summary.software_version.patch' must be an integer")
    
    software_version = SoftwareVersion(major=major, minor=minor, patch=patch)
    
    return RunSummary(
        status=status,
        total_reads=total_reads,
        software_version=software_version,
        mapped_reads=mapped_reads,
        mapped_reads_percentage=float(mapped_reads_percentage)
    )


def _parse_detections(data: Mapping[str, JSONValue]) -> List[Detection]:
    """Parse detections array."""
    detections_data = data.get("detections")
    if not isinstance(detections_data, Sequence):
        raise ValueError("JSON must contain a 'detections' array")

    detections = []
    for i, detection_obj in enumerate(detections_data):
        if not isinstance(detection_obj, Mapping):
            raise ValueError(f"Detection at index {i} must be an object")

        # Parse detection fields
        reference_genome = detection_obj.get("reference_genome")
        pathogen_type = detection_obj.get("type")
        confidence = detection_obj.get("confidence")
        clinical_significance = detection_obj.get("clinical_significance")
        coverage_score = detection_obj.get("coverage_score")
        soc_count = detection_obj.get("SoC_count")
        alert_level = detection_obj.get("alert_level")

        # Validate types
        if not isinstance(reference_genome, str):
            raise ValueError(f"Detection at index {i}: 'reference_genome' must be a string")
        if not isinstance(pathogen_type, str):
            raise ValueError(f"Detection at index {i}: 'type' must be a string")
        if not isinstance(confidence, (int, float)):
            raise ValueError(f"Detection at index {i}: 'confidence' must be a number")
        if not isinstance(clinical_significance, str):
            raise ValueError(f"Detection at index {i}: 'clinical_significance' must be a string")
        if not isinstance(coverage_score, (int, float)):
            raise ValueError(f"Detection at index {i}: 'coverage_score' must be a number")
        if not isinstance(soc_count, int):
            raise ValueError(f"Detection at index {i}: 'SoC_count' must be an integer")
        if not isinstance(alert_level, str):
            raise ValueError(f"Detection at index {i}: 'alert_level' must be a string")

        # Validate enums
        try:
            pathogen_type_enum = PathogenType(pathogen_type)
        except ValueError:
            valid_types = [t.value for t in PathogenType]
            raise ValueError(f"Detection at index {i}: 'type' must be one of {valid_types}, got: {pathogen_type}")

        try:
            clinical_significance_enum = ClinicalSignificance(clinical_significance)
        except ValueError:
            valid_significance = [s.value for s in ClinicalSignificance]
            raise ValueError(f"Detection at index {i}: 'clinical_significance' must be one of {valid_significance}, got: {clinical_significance}")

        try:
            alert_level_enum = AlertLevel(alert_level)
        except ValueError:
            valid_alert_levels = [a.value for a in AlertLevel]
            raise ValueError(f"Detection at index {i}: 'alert_level' must be one of {valid_alert_levels}, got: {alert_level}")

        detection = Detection(
            reference_genome=reference_genome,
            type=pathogen_type_enum,
            confidence=float(confidence),
            clinical_significance=clinical_significance_enum,
            coverage_score=float(coverage_score),
            SoC_count=soc_count,
            alert_level=alert_level_enum
        )
        detections.append(detection)

    return detections


def _parse_interpretations(data: Mapping[str, JSONValue]) -> List[InterpretationNote]:
    """Parse interpretations array."""
    interpretations_data = data.get("interpretations")
    if not isinstance(interpretations_data, Sequence):
        raise ValueError("JSON must contain an 'interpretations' array")

    interpretations = []
    for i, interpretation_obj in enumerate(interpretations_data):
        if not isinstance(interpretation_obj, Mapping):
            raise ValueError(f"Interpretation at index {i} must be an object")

        # Parse interpretation fields
        text = interpretation_obj.get("text")
        style = interpretation_obj.get("style")

        if not isinstance(text, str):
            raise ValueError(f"Interpretation at index {i}: 'text' must be a string")
        if not isinstance(style, str):
            raise ValueError(f"Interpretation at index {i}: 'style' must be a string")

        try:
            style_enum = TextStyle(style)
        except ValueError:
            valid_styles = [s.value for s in TextStyle]
            raise ValueError(f"Interpretation at index {i}: 'style' must be one of {valid_styles}, got: {style}")

        interpretation = InterpretationNote(text=text, style=style_enum)
        interpretations.append(interpretation)

    return interpretations


def _parse_test_performance(data: Mapping[str, JSONValue]) -> TestPerformance:
    """Parse test_performance section."""
    test_performance_data = data.get("test_performance")
    if not isinstance(test_performance_data, Mapping):
        raise ValueError("JSON must contain a 'test_performance' object")

    # Parse test performance fields
    assay = test_performance_data.get("assay")
    limit_of_detection = test_performance_data.get("limit_of_detection")
    sequencing_platform = test_performance_data.get("sequencing_platform")
    reads_processed = test_performance_data.get("reads_processed")

    if not isinstance(assay, str):
        raise ValueError("'test_performance.assay' must be a string")
    if not isinstance(limit_of_detection, str):
        raise ValueError("'test_performance.limit_of_detection' must be a string")
    if not isinstance(sequencing_platform, str):
        raise ValueError("'test_performance.sequencing_platform' must be a string")
    if not isinstance(reads_processed, str):
        raise ValueError("'test_performance.reads_processed' must be a string")

    # Parse turnaround time
    turnaround_time_data = test_performance_data.get("turnaround_time")
    if not isinstance(turnaround_time_data, Mapping):
        raise ValueError("JSON must contain a 'test_performance.turnaround_time' object")

    prep_hours = turnaround_time_data.get("prep_hours")
    sequencing_hours = turnaround_time_data.get("sequencing_hours")
    analysis_hours = turnaround_time_data.get("analysis_hours")

    if not isinstance(prep_hours, int):
        raise ValueError("'test_performance.turnaround_time.prep_hours' must be an integer")
    if not isinstance(sequencing_hours, int):
        raise ValueError("'test_performance.turnaround_time.sequencing_hours' must be an integer")
    if not isinstance(analysis_hours, int):
        raise ValueError("'test_performance.turnaround_time.analysis_hours' must be an integer")

    turnaround_time = TurnaroundTime(
        prep_hours=prep_hours,
        sequencing_hours=sequencing_hours,
        analysis_hours=analysis_hours
    )

    return TestPerformance(
        assay=assay,
        turnaround_time=turnaround_time,
        limit_of_detection=limit_of_detection,
        sequencing_platform=sequencing_platform,
        reads_processed=reads_processed
    )
