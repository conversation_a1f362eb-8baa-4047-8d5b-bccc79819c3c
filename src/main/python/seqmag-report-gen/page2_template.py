"""
Page 2 template for HTML report generation.

This module provides the template for the second page of the SeqMag report,
which contains interpretations and test performance information.
"""

import re
from .page_template import PageTemplate
from .html_manipulation import replace_html_element_content_by_id
from .report_data import ReportData, InterpretationNote, TextStyle, TestPerformance, TurnaroundTime, SoftwareVersion


class Page2Template(PageTemplate):
    """
    Template for the second page with interpretations and test performance.
    
    This page includes:
    - Interpretation & recommendations list
    - Test performance metrics (assay, turnaround time, etc.)
    - Software version and database information
    - Disclaimer text
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str, page_content_html: str):
        """
        Initialize the Page 2 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
            page_content_html: The page 2 specific content HTML
        """
        super().__init__(report_html_template, header_html, footer_html, page_content_html, page_number=2)

    def populate_content(self, report: ReportData) -> str:
        """
        Populate the page 2 content HTML with report data.

        Args:
            report: The report data to use for content population

        Returns:
            str: HTML string with populated page 2 content
        """
        result_html = self.page_content_html

        # Populate interpretation-list with interpretation notes
        def generate_interpretation_list_items(interpretations: list[InterpretationNote]) -> str:
            if not interpretations:
                return '<li style="font-style: italic;">No interpretations available</li>'

            items: list[str] = []
            for interpretation in interpretations:
                # Apply styling based on the TextStyle enum
                if interpretation.style == TextStyle.BOLD:
                    item: str = f'<li><strong>{interpretation.text}</strong></li>'
                else:  # TextStyle.NORMAL
                    item = f'<li>{interpretation.text}</li>'
                items.append(item)

            return "\n".join(items)

        # Find and replace the content in interpretation-list
        interpretation_items: str = generate_interpretation_list_items(list(report.interpretations))
        interpretation_list_pattern: str = r'(<ul[^>]*id\s*=\s*["\']interpretation-list["\'][^>]*>)(.*?)(</ul>)'
        result_html = re.sub(
            interpretation_list_pattern,
            lambda m: f"{m.group(1)}\n{interpretation_items}\n{m.group(3)}",
            result_html,
            flags=re.IGNORECASE | re.DOTALL
        )

        # Populate test performance elements
        test_performance: TestPerformance = report.test_performance

        # Replace assay element content
        assay_value: str = test_performance.assay
        result_html = replace_html_element_content_by_id(result_html, "assay", assay_value)

        # Replace turnaround-time element content with formatted breakdown
        def format_turnaround_time(turnaround: TurnaroundTime) -> str:
            total_hours: int = turnaround.prep_hours + turnaround.sequencing_hours + turnaround.analysis_hours
            return f"Total time {total_hours} hours ({turnaround.prep_hours} hour prep and extraction, {turnaround.sequencing_hours} hours sequencing, {turnaround.analysis_hours} hour SeqMag analysis)"

        turnaround_time_value: str = format_turnaround_time(test_performance.turnaround_time)
        result_html = replace_html_element_content_by_id(result_html, "turnaround-time", turnaround_time_value)

        # Replace limit-of-detection element content
        limit_of_detection_value: str = test_performance.limit_of_detection
        result_html = replace_html_element_content_by_id(result_html, "limit-of-detection", limit_of_detection_value)

        # Replace sequencing-platform element content
        sequencing_platform_value: str = test_performance.sequencing_platform
        result_html = replace_html_element_content_by_id(result_html, "sequencing-platform", sequencing_platform_value)

        # Replace reads-processed-performance element content
        reads_processed_performance_value: str = test_performance.reads_processed
        result_html = replace_html_element_content_by_id(result_html, "reads-processed-performance", reads_processed_performance_value)

        # Replace software-version-database element content (vX.Y.Z format)
        software_version_database: SoftwareVersion = report.run_summary.software_version
        software_version_database_value: str = f"v{software_version_database.major}.{software_version_database.minor}.{software_version_database.patch}"
        result_html = replace_html_element_content_by_id(result_html, "software-version-database", software_version_database_value)

        return result_html
    

