"""
Page 2 template for HTML report generation.

This module provides the template for the second page of the SeqMag report,
which contains interpretations and test performance information.
"""

from .page_template import PageTemplate
from .report_data import ReportData


class Page2Template(PageTemplate):
    """
    Template for the second page with interpretations and test performance.
    
    This page includes:
    - Interpretation & recommendations list
    - Test performance metrics (assay, turnaround time, etc.)
    - Software version and database information
    - Disclaimer text
    """
    
    def __init__(self, report_html_template: str, header_html: str, footer_html: str):
        """
        Initialize the Page 2 template.

        Args:
            report_html_template: The HTML template string to use for this page
            header_html: The header HTML template content
            footer_html: The footer HTML template content
        """
        super().__init__(report_html_template, header_html, footer_html, page_number=2)
    
    def generate_content(self, report: ReportData) -> str:
        """
        Generate the content for page 2.
        
        Args:
            report: The report data (not used in template generation, 
                   but required by base class interface)
            
        Returns:
            str: HTML string for page 2 content
        """
        return '''        <div class="section">
            <div class="section-title">Interpretation & Recommendations</div>
            <ul id="interpretation-list" class="section-non-title">
            </ul>
        </div>
        <div id="test-performance" class="section">
            <div class="section-title">Test Performance</div>
            <div class="section-non-title">
                <div class="test-performance-row" >
                    <span>Assay:</span>
                    <span id="assay"></span>
                </div>
                <div class="test-performance-row">
                    <span>Turnaround Time:</span>
                    <span id="turnaround-time"></span>
                </div>
                <div class="test-performance-row">
                    <span>Limit of Detection:</span>
                    <span id="limit-of-detection"></span>
                </div>
                <div class="test-performance-row">
                    <span>Sequencing Platform:</span>
                    <span id="sequencing-platform"></span>
                </div>
                <div class="test-performance-row">
                    <span>Reads Processed:</span>
                    <span id="reads-processed-performance"></span>
                </div>
            </div>
        </div>
        <div class="section">
            <div class="section-title">Software Version and Database</div>
            <div class="section-non-title">
                <div id="software-version-database" class="database-row"></div>
                <div class="database-row">
                    <a href="https://genomebiology.biomedcentral.com/articles/10.1186/s13059-022-02695-x">SeqScreen</a>
                </div>
            </div>
        </div>
        <div class="section">
            <div class="section-title">Disclaimer</div>
            <div class="section-non-title">
                <div class="disclaimer-row">
                    This test is intended as a diagnostic support tool. Clinical judgement and other diagnostic findings should guide
                    management decisions.
                </div>
            </div>
        </div>'''
