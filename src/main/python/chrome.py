from __future__ import annotations

import shlex
import subprocess
from pathlib import Path
from typing import Iterable, Optional


def build_chrome_cmd(
    output_pdf: Path,
    input_html: Path,
    extra_args: Optional[Iterable[str]] = None,
) -> list[str]:
    """
    Build the headless Chrome command. Assumes 'google-chrome' is on PATH.
    """
    cmd = [
        "google-chrome",
        "--headless",
        "--disable-gpu",
        f"--print-to-pdf={str(output_pdf)}",
        "--no-pdf-header-footer",
        "--run-all-compositor-stages-before-draw",
        "--virtual-time-budget=10000",
        input_html.as_uri(),
    ]
    if extra_args:
        cmd.extend(extra_args)
    return cmd


def run(cmd: list[str]) -> None:
    """
    Run a command, raising on non-zero exit.
    """
    print("Running:", " ".join(shlex.quote(c) for c in cmd))
    subprocess.run(cmd, check=True)
