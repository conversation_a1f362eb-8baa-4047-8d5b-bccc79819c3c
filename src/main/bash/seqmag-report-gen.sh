#!/usr/bin/env bash
set -euo pipefail

# Usage/help
if [ $# -ne 2 ]; then
    echo "Usage: $0 <report_json_path> <output_pdf_path>" >&2
    echo "  report_json_path: Path to JSON file with values for the template (required)" >&2
    echo "  output_pdf_path:  Path to the output PDF file (required)" >&2
    exit 1
fi

# Resolve this script's directory (works with symlinks)
SCRIPT_DIR="$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd -P)"

# Ensure Python can import the local package/module "scripts" next to this file
export PYTHONPATH="${SCRIPT_DIR}${PYTHONPATH:+:$PYTHONPATH}"

REPORT_JSON_PATH="$1"
OUTPUT_PDF_PATH="$2"

# Prefer python3, fall back to python
PYTHON_BIN="${PYTHON_BIN:-python3}"
command -v "$PYTHON_BIN" >/dev/null 2>&1 || PYTHON_BIN=python
command -v "$PYTHON_BIN" >/dev/null 2>&1 || { echo "Python not found." >&2; exit 2; }

# Run the module from anywhere
"$PYTHON_BIN" -m scripts "$REPORT_JSON_PATH" "$OUTPUT_PDF_PATH"
