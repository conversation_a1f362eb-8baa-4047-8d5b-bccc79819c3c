
#report
{
    max-width: 7.5in;
}

.page
{
    position: relative;
    min-height: 10in;
    max-height: 10in;
}

.header
{
    display: flex;
    justify-content: space-between;
    padding: 10px;
    margin-bottom: 20px;
}

.header > img
{
    height: 50px;
}

.header-title
{
    display: flex;
    align-items: end;
    justify-content: center;
    font-size: 25px;
    font-weight: bold;
}

.footer
{
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 12px
}

#metadata-section
{
    display: flex;
    justify-content: space-between;
    margin: 10px;
}

.metadata-subsection
{
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    border-color: black;
    border-style: solid;
    border-width: 1px;
    padding: 5px;
}

.metadata-subsection-title
{
    font-weight: bold;
    font-size: 16px;
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
}

.metadata-subsection-non-title
{
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    flex-grow: 1;
}

.metadata-subsection-non-title > div
{
    margin: 3px;
}

.metadata-key
{
    font-size: 12px;
    font-weight: bold;
}

.metadata-value
{
    font-size: 12px;
}

.green-metadata-value
{
    color: green;
}

.section
{
    margin-top: 40px;
}

.section-title
{
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: bold;
}

.section-non-title *
{
    font-size: 14px;
}


.alert-container
{
    display: flex;
    font-size: 12px;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 10px;
}

.alert-icons-container
{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.alert-light
{
    width: 35px;
    margin-left: 10px;
}

#red-alert
{
    color: white;
    border: 1px solid red;
}

#yellow-alert
{
    color: black;
    border: 1px solid #e4d523;
}

#green-alert
{
    color: black;
    border: 1px solid green;
}

.alert-non-icons-container
{
    flex-grow: 1;
    margin-left: 10px;
}

.alert-row
{
    display: flex;
    font-size: 16px;
    color: black;
    margin-top: 5px;
    padding: 5px;
}

.alert-row-red
{
    background-color: mistyrose;
}

.alert-row-yellow
{
    background-color: #fff8e1;
}

.alert-row-green
{
    background-color: #bef8be;
}

.alert-organism-name
{
    font-weight: bold;
    font-style: italic;
    margin-left: 20px;
}

.alert-title-major-text
{
    color: black;
    font-size: 15px;
    font-weight: bold;
}

.alert-title-minor-text
{
    color: black;
    font-size: 10px;
}

.alert-row-organism-type
{
    width: 60px;
    display: inline-block;
    text-align: right;
}

#reads-table {
    width: 100%;
    border-collapse: collapse;
    font-family: "Segoe UI", Roboto, sans-serif;
    background-color: #fff;
    color: #1f2937;
    border: 1px solid #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

#reads-table thead {
    background-color: #f0f4f8;
}

#reads-table th,
#reads-table td {
    border: 1px solid #ccc;
    padding: 10px 14px;
    font-size: 12px;
    text-align: center;
}

#reads-table tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

#reads-table tbody tr:nth-child(odd) {
    background-color: #ffffff;
}

#reads-table tbody tr:hover {
    background-color: #eef2f7;
}


#interpretation-list > li
{
    margin-top: 10px;
}

.test-performance-row
{
    font-size: 20px;
    margin-top: 20px;
    margin-left: 20px;
}

.database-row
{
    margin-top: 20px;
    margin-left: 20px;
}

.disclaimer-row {
    margin-top: 20px;
    margin-left: 20px;
}

.consultation-row
{
    margin-top: 20px;
    margin-left: 20px;
}
