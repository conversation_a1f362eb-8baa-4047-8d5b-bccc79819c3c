#!/usr/bin/env bash

set -e  # exit immediately if any command exits with a non-zero status

# Establish path variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)" # path to the directory containing this script
BUILD_DIR="$SCRIPT_DIR/build"

# Make the build output directory if it doesn't exist
mkdir -p "$BUILD_DIR"

# Clean build output directory
rm -rf "$BUILD_DIR"/*

# Create build output subdirectories
mkdir "$BUILD_DIR/template"
mkdir "$BUILD_DIR/scripts"

# Copy web files
cp -r "$SCRIPT_DIR/src/main/web"/* "$BUILD_DIR/template"

# Copy scripts
cp "$SCRIPT_DIR/src/main/bash"/*.sh "$BUILD_DIR"

# Copy the python files
cp "$SCRIPT_DIR/src/main/python/seqmag-report-gen/"*.py "$BUILD_DIR/scripts"
