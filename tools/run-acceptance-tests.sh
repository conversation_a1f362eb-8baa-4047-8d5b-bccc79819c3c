#!/usr/bin/env bash

# run-acceptance-tests.sh
# Automated acceptance testing script for seqmag-report-gen
#
# This script:
# 1. Finds all JSON files in the aat/ directory
# 2. Generates PDFs using seqmag-report-gen for each JSON file (next to the JSON)
# 3. For files starting with "invalid", expects seqmag-report-gen to FAIL (non-zero); no diff is run
# 4. For others, compares generated PDFs with expected PDFs using diff-pdf
# 5. Saves visual diffs next to the PDFs as *.diff.pdf if different
# 6. Reports test results
#
# Prerequisites:
# - seqmag-report-gen must be built in build/ directory
# - diff-pdf must be installed and available in PATH
# - aat/ directory must contain JSON files and (for non-invalid tests) corresponding expected PDF files
#
# Usage: ./tools/run-acceptance-tests.sh

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
AAT_DIR="$PROJECT_ROOT/aat"
SEQMAG_SCRIPT="$PROJECT_ROOT/build/seqmag-report-gen.sh"

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo -e "${BLUE}SeqMag Report Generator - Acceptance Tests${NC}"
echo "=============================================="
echo

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if [ ! -f "$SEQMAG_SCRIPT" ]; then
    echo -e "${RED}ERROR: seqmag-report-gen script not found at $SEQMAG_SCRIPT${NC}"
    echo "Please build the project first (run ./build.sh)"
    exit 1
fi

if [ ! -x "$SEQMAG_SCRIPT" ]; then
    echo -e "${RED}ERROR: seqmag-report-gen script is not executable at $SEQMAG_SCRIPT${NC}"
    echo "Please make sure the build script is executable"
    exit 1
fi

if ! command -v diff-pdf >/dev/null 2>&1; then
    echo -e "${RED}ERROR: diff-pdf not found in PATH${NC}"
    echo "Please install diff-pdf (e.g., 'apt install diffpdf' or 'brew install diff-pdf')"
    exit 1
fi

if [ ! -d "$AAT_DIR" ]; then
    echo -e "${RED}ERROR: aat/ directory not found at $AAT_DIR${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Prerequisites check passed${NC}"
echo

echo -e "${BLUE}Running acceptance tests...${NC}"
echo

# Find all JSON files in aat directory (robust to spaces)
mapfile -d '' JSON_FILES < <(find "$AAT_DIR" -type f -name '*.json' -print0 | sort -z)

if [ ${#JSON_FILES[@]} -eq 0 ]; then
    echo -e "${YELLOW}WARNING: No JSON files found in $AAT_DIR${NC}"
    exit 0
fi

echo "Found ${#JSON_FILES[@]} test file(s)"
echo

# Process each JSON file
for json_file in "${JSON_FILES[@]}"; do
    json_file="${json_file%$'\0'}"  # remove trailing NUL
    base_dir="$(dirname "$json_file")"
    base_name="$(basename "$json_file" .json)"

    expected_pdf="$base_dir/$base_name.pdf"
    generated_pdf="$base_dir/$base_name.generated.pdf"
    diff_pdf="$base_dir/$base_name.diff.pdf"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}Test $TOTAL_TESTS: $base_name${NC}"

    # Negative tests: filenames starting with "invalid"
    if [[ "$base_name" == invalid* ]]; then
        echo "  Negative test detected (expects non-zero exit from generator)."
        # Clean any stale artifacts
        rm -f -- "$generated_pdf" "$diff_pdf"

        set +e
        "$SEQMAG_SCRIPT" "$json_file" "$generated_pdf" 2>/dev/null
        gen_status=$?
        set -e

        if [ $gen_status -eq 0 ]; then
            echo -e "${RED}  ✗ FAIL: Expected seqmag-report-gen to fail, but it succeeded${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        else
            echo -e "${GREEN}  ✓ PASS: seqmag-report-gen failed as expected (exit $gen_status)${NC}"
            # Ensure no artifacts linger
            rm -f -- "$generated_pdf" "$diff_pdf"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi

        echo
        continue
    fi

    # Positive tests (non-invalid): must have expected PDF
    if [ ! -f "$expected_pdf" ]; then
        echo -e "${RED}  ✗ FAIL: Expected PDF not found: $expected_pdf${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo
        continue
    fi

    # Generate PDF using seqmag-report-gen
    echo "  Generating PDF from $json_file..."
    if ! "$SEQMAG_SCRIPT" "$json_file" "$generated_pdf" 2>/dev/null; then
        echo -e "${RED}  ✗ FAIL: PDF generation failed${NC}"
        echo -e "${YELLOW}    Command that failed: $SEQMAG_SCRIPT $json_file $generated_pdf${NC}"
        # Check if a partial PDF was generated and preserve it for debugging
        if [ -f "$generated_pdf" ]; then
            echo -e "${YELLOW}    Partial PDF preserved: $generated_pdf${NC}"
        fi
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo
        continue
    fi

    # Check if generated PDF exists
    if [ ! -f "$generated_pdf" ]; then
        echo -e "${RED}  ✗ FAIL: Generated PDF not found: $generated_pdf${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo
        continue
    fi

    # Remove any stale diff file before comparing
    [ -f "$diff_pdf" ] && rm -f -- "$diff_pdf"

    # Compare PDFs using diff-pdf
    echo "  Comparing with expected PDF..."
    if diff-pdf --output-diff="$diff_pdf" "$expected_pdf" "$generated_pdf" >/dev/null 2>&1; then
        echo -e "${GREEN}  ✓ PASS: PDFs match${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        # Clean up artifacts if they are identical
        rm -f -- "$generated_pdf" "$diff_pdf"
    else
        status=$?
        if [ $status -eq 1 ]; then
            echo -e "${RED}  ✗ FAIL: PDFs differ${NC}"
            echo -e "${YELLOW}    Generated PDF preserved: $generated_pdf${NC}"
            echo -e "${YELLOW}    Diff saved to: $diff_pdf${NC}"
        else
            echo -e "${RED}  ✗ FAIL: diff-pdf error (exit $status)${NC}"
            echo -e "${YELLOW}    Generated PDF preserved: $generated_pdf${NC}"
            # Remove diff file if diff-pdf failed to create it properly
            rm -f -- "$diff_pdf"
        fi
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    echo
done

# Print summary
echo "=============================================="
echo -e "${BLUE}Test Summary${NC}"
echo "=============================================="
echo "Total tests:  $TOTAL_TESTS"
echo -e "Passed:       ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed:       ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo
    echo -e "${RED}❌ $FAILED_TESTS test(s) failed${NC}"
    echo -e "${YELLOW}Debugging artifacts preserved for failed tests:${NC}"
    echo -e "${YELLOW}  - *.generated.pdf: The PDF generated by seqmag-report-gen${NC}"
    echo -e "${YELLOW}  - *.diff.pdf: Visual diff between expected and generated PDFs${NC}"
    exit 1
fi
